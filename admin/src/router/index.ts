import { createRouter, createWebHistory } from 'vue-router'
import AdminLayout from '@/views/AdminLayout.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      redirect: '/admin'
    },
    {
      path: '/admin',
      component: AdminLayout,
      children: [
        {
          path: '',
          redirect: '/admin/dashboard'
        },
        {
          path: 'dashboard',
          name: 'Dashboard',
          component: () => import('@/views/Dashboard.vue'),
          meta: { title: '仪表盘' }
        },
        {
          path: 'students',
          name: 'Students',
          component: () => import('@/views/Students.vue'),
          meta: { title: '学生管理' }
        },
        {
          path: 'classes',
          name: 'Classes',
          component: () => import('@/views/Classes.vue'),
          meta: { title: '班级管理' }
        },
        {
          path: 'experiments',
          name: 'Experiments',
          component: () => import('@/views/Experiments.vue'),
          meta: { title: '实验管理' }
        },
        {
          path: 'learning-stats',
          name: 'LearningStats',
          component: () => import('@/views/LearningStats.vue'),
          meta: { title: '学习统计' }
        },
        {
          path: 'records',
          name: 'Records',
          component: () => import('@/views/Records.vue'),
          meta: { title: '实验记录' }
        },
        {
          path: 'current-control-circuit',
          name: 'CurrentControlCircuit',
          component: () => import('@/views/CurrentControlCircuit.vue'),
          meta: { title: '制流电路实验' }
        }
      ]
    }
  ]
})

export default router
