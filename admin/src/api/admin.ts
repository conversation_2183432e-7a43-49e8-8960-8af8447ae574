/**
 * 管理后台相关API
 */

import { request } from './index'

export interface AdminRecordListParams {
  page?: number
  page_size?: number
  experiment_code?: string
  student_no?: string
  student_name?: string
  date_from?: string
  date_to?: string
  status?: string
}

export const adminApi = {
  // 仪表盘概览统计
  getOverviewStats() {
    return request.get('/api/admin/statistics/overview')
  },

  // 实验统计（按实验类型聚合）
  getExperimentStatistics() {
    return request.get('/api/admin/statistics/experiments')
  },

  // 学生列表（可选按班级筛选）
  getStudents(params?: { class_id?: number; include_unassigned?: boolean }) {
    return request.get('/api/admin/students', { params })
  },

  // 班级列表
  getClasses() {
    return request.get('/api/admin/classes')
  },

  // 创建班级
  createClass(data: { name: string; code: string; department?: string; grade?: number }) {
    return request.post('/api/admin/classes', data)
  },

  // 更新班级
  updateClass(id: number, data: { name?: string; code?: string; department?: string; grade?: number }) {
    return request.put(`/api/admin/classes/${id}`, data)
  },

  // 删除班级
  deleteClass(id: number) {
    return request.delete(`/api/admin/classes/${id}`)
  },

  // 创建学生
  createStudent(data: { student_id: string; name: string; class_id: number; group_id?: number; email?: string }) {
    return request.post('/api/admin/students', data)
  },

  // 更新学生
  updateStudent(id: number, data: { student_id?: string; name?: string; class_id?: number; group_id?: number; email?: string; is_active?: boolean }) {
    return request.put(`/api/admin/students/${id}`, data)
  },

  // 删除学生
  deleteStudent(id: number) {
    return request.delete(`/api/admin/students/${id}`)
  },

  // 批量导入学生
  importStudents(file: File) {
    const formData = new FormData()
    formData.append('file', file)
    return request.post('/api/admin/students/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 获取学生详情
  getStudentDetail(studentId: number) {
    return request.get(`/api/admin/students/${studentId}/detail`)
  },

  // 获取学生实验记录
  getStudentRecords(studentId: number, experimentCode?: string) {
    const params = experimentCode ? { experiment_code: experimentCode } : {}
    return request.get(`/api/admin/students/${studentId}/records`, { params })
  },

  // 获取制流电路实验结果
  getCurrentControlCircuitResults(studentId?: string) {
    const params = studentId ? { student_id: studentId } : {}
    return request.get('/api/admin/experiments/current-control-circuit', { params })
  },

  // 获取制流电路实验详情
  getCurrentControlCircuitDetail(recordId: number) {
    return request.get(`/api/admin/experiments/current-control-circuit/${recordId}`)
  },

  // 获取实验类型列表
  getExperimentTypes() {
    return request.get('/api/experiments/types')
  },

  // 获取实验记录（分页）
  getExperimentRecords(params: AdminRecordListParams) {
    return request.get('/api/admin/records', { params })
  },

  // 获取单条记录详情
  getExperimentRecordDetail(recordId: number) {
    return request.get(`/api/admin/records/${recordId}`)
  },

  // 获取学习进度统计
  getLearningProgressStats(params?: { class_id?: number; group_id?: number }) {
    return request.get('/api/admin/statistics/progress', { params })
  },

  // 获取提交趋势统计
  getSubmissionTrends(params?: { days?: number; class_id?: number }) {
    return request.get('/api/admin/statistics/trends', { params })
  }
}
