<template>
  <div class="learning-stats">
    <div class="page-header">
      <h2>学习情况统计</h2>
      <div class="filters">
        <el-select
          v-model="selectedClassId"
          placeholder="选择班级"
          clearable
          @change="fetchStats"
          style="width: 150px; margin-right: 10px"
        >
          <el-option
            v-for="cls in classes"
            :key="cls.id"
            :label="cls.name"
            :value="cls.id"
          />
        </el-select>
        <el-button @click="fetchStats" :loading="loading">刷新</el-button>
      </div>
    </div>

    <el-card>
      <el-tabs v-model="activeTab" @tab-change="handleTabChange">
        <!-- 班级统计 -->
        <el-tab-pane label="班级统计" name="class">
          <el-table :data="stats.class_stats" stripe>
            <el-table-column prop="class_name" label="班级名称" />
            <el-table-column prop="class_code" label="班级代码" />
            <el-table-column prop="total_students" label="学生数" />
            <el-table-column prop="total_submissions" label="提交数" />
            <el-table-column prop="passed_submissions" label="通过数" />
            <el-table-column prop="pass_rate" label="通过率">
              <template #default="{ row }">
                <el-tag :type="getPassRateType(row.pass_rate)">
                  {{ row.pass_rate }}%
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="avg_score" label="平均分" />
          </el-table>
        </el-tab-pane>

        <!-- 分组统计 -->
        <el-tab-pane label="分组统计" name="group">
          <el-table :data="stats.group_stats" stripe>
            <el-table-column prop="group_name" label="分组名称" />
            <el-table-column prop="class_name" label="所属班级" />
            <el-table-column prop="total_students" label="学生数" />
            <el-table-column prop="total_submissions" label="提交数" />
            <el-table-column prop="passed_submissions" label="通过数" />
            <el-table-column prop="pass_rate" label="通过率">
              <template #default="{ row }">
                <el-tag :type="getPassRateType(row.pass_rate)">
                  {{ row.pass_rate }}%
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="avg_score" label="平均分" />
          </el-table>
        </el-tab-pane>

        <!-- 学生统计 -->
        <el-tab-pane label="学生统计" name="student">
          <el-table :data="stats.student_stats" stripe>
            <el-table-column prop="student_no" label="学号" />
            <el-table-column prop="student_name" label="姓名" />
            <el-table-column prop="class_name" label="班级" />
            <el-table-column prop="group_name" label="分组" />
            <el-table-column prop="total_submissions" label="提交数" />
            <el-table-column prop="passed_submissions" label="通过数" />
            <el-table-column prop="pass_rate" label="通过率">
              <template #default="{ row }">
                <el-tag :type="getPassRateType(row.pass_rate)">
                  {{ row.pass_rate }}%
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="avg_score" label="平均分" />
            <el-table-column prop="last_submission_at" label="最近提交">
              <template #default="{ row }">
                {{ formatDate(row.last_submission_at) }}
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>

        <!-- 实验覆盖率 -->
        <el-tab-pane label="实验覆盖率" name="experiment">
          <el-table :data="stats.experiment_stats" stripe>
            <el-table-column prop="experiment_name" label="实验名称" />
            <el-table-column prop="experiment_code" label="实验代码" />
            <el-table-column prop="total_submissions" label="提交数" />
            <el-table-column prop="passed_submissions" label="通过数" />
            <el-table-column prop="pass_rate" label="通过率">
              <template #default="{ row }">
                <el-tag :type="getPassRateType(row.pass_rate)">
                  {{ row.pass_rate }}%
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="unique_students" label="参与学生数" />
            <el-table-column prop="coverage_rate" label="覆盖率">
              <template #default="{ row }">
                <el-tag :type="getCoverageType(row.coverage_rate)">
                  {{ row.coverage_rate }}%
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>

        <!-- 提交趋势 -->
        <el-tab-pane label="提交趋势" name="trends">
          <div class="trends-container">
            <div class="trend-filters">
              <el-radio-group v-model="trendDays" @change="fetchTrends">
                <el-radio-button :label="7">最近7天</el-radio-button>
                <el-radio-button :label="30">最近30天</el-radio-button>
                <el-radio-button :label="90">最近90天</el-radio-button>
              </el-radio-group>
            </div>
            <div class="trend-chart" ref="trendChart" style="height: 400px;"></div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { adminApi } from '@/api/admin'
import * as echarts from 'echarts'

// 响应式数据
const loading = ref(false)
const activeTab = ref('class')
const selectedClassId = ref<number | null>(null)
const trendDays = ref(30)
const classes = ref<any[]>([])
const trendChart = ref<HTMLElement>()
let chartInstance: echarts.ECharts | null = null

const stats = reactive({
  class_stats: [],
  group_stats: [],
  student_stats: [],
  experiment_stats: []
})

const trends = ref<any[]>([])

// 获取班级列表
const fetchClasses = async () => {
  try {
    const res = await adminApi.getClasses()
    classes.value = (res as any) as any[]
  } catch (error) {
    console.error('获取班级列表失败:', error)
  }
}

// 获取学习统计数据
const fetchStats = async () => {
  loading.value = true
  try {
    const params: any = {}
    if (selectedClassId.value) {
      params.class_id = selectedClassId.value
    }
    
    const res = await adminApi.getLearningProgressStats(params)
    const data = res as any
    
    stats.class_stats = data.class_stats || []
    stats.group_stats = data.group_stats || []
    stats.student_stats = data.student_stats || []
    stats.experiment_stats = data.experiment_stats || []
    
  } catch (error) {
    console.error('获取学习统计失败:', error)
    ElMessage.error('获取学习统计失败')
  } finally {
    loading.value = false
  }
}

// 获取趋势数据
const fetchTrends = async () => {
  try {
    const params: any = { days: trendDays.value }
    if (selectedClassId.value) {
      params.class_id = selectedClassId.value
    }
    
    const res = await adminApi.getSubmissionTrends(params)
    trends.value = (res as any) as any[]
    
    await nextTick()
    renderTrendChart()
  } catch (error) {
    console.error('获取趋势数据失败:', error)
    ElMessage.error('获取趋势数据失败')
  }
}

// 渲染趋势图表
const renderTrendChart = () => {
  if (!trendChart.value || trends.value.length === 0) return
  
  if (chartInstance) {
    chartInstance.dispose()
  }
  
  chartInstance = echarts.init(trendChart.value)
  
  const option = {
    title: {
      text: '提交趋势统计',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['总提交数', '通过数', '通过率'],
      top: 30
    },
    xAxis: {
      type: 'category',
      data: trends.value.map(item => item.date)
    },
    yAxis: [
      {
        type: 'value',
        name: '提交数',
        position: 'left'
      },
      {
        type: 'value',
        name: '通过率(%)',
        position: 'right',
        max: 100
      }
    ],
    series: [
      {
        name: '总提交数',
        type: 'line',
        data: trends.value.map(item => item.total_submissions),
        smooth: true
      },
      {
        name: '通过数',
        type: 'line',
        data: trends.value.map(item => item.passed_submissions),
        smooth: true
      },
      {
        name: '通过率',
        type: 'line',
        yAxisIndex: 1,
        data: trends.value.map(item => item.pass_rate),
        smooth: true
      }
    ]
  }
  
  chartInstance.setOption(option)
}

// 标签页切换
const handleTabChange = (tabName: string) => {
  if (tabName === 'trends') {
    fetchTrends()
  }
}

// 通过率标签类型
const getPassRateType = (rate: number) => {
  if (rate >= 80) return 'success'
  if (rate >= 60) return 'warning'
  return 'danger'
}

// 覆盖率标签类型
const getCoverageType = (rate: number) => {
  if (rate >= 80) return 'success'
  if (rate >= 50) return 'warning'
  return 'danger'
}

// 格式化日期
const formatDate = (dateStr: string | null) => {
  if (!dateStr) return '-'
  return new Date(dateStr).toLocaleString('zh-CN')
}

// 组件挂载
onMounted(() => {
  fetchClasses()
  fetchStats()
})
</script>

<style scoped>
.learning-stats {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.filters {
  display: flex;
  align-items: center;
}

.trends-container {
  padding: 20px 0;
}

.trend-filters {
  margin-bottom: 20px;
  text-align: center;
}

.trend-chart {
  width: 100%;
  height: 400px;
}
</style>
