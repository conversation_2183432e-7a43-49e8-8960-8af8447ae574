<template>
  <div class="admin-records">
    <div class="page-header">
      <h2>实验记录</h2>
    </div>
    
    <!-- 搜索和筛选 -->
    <div class="search-bar">
      <el-row :gutter="20">
        <el-col :span="4">
          <el-input
            v-model="searchParams.student_no"
            placeholder="学号"
            clearable
          />
        </el-col>
        <el-col :span="4">
          <el-input
            v-model="searchParams.student_name"
            placeholder="学生姓名"
            clearable
          />
        </el-col>
        <el-col :span="4">
          <el-input
            v-model="searchParams.experiment_code"
            placeholder="实验代码"
            clearable
          />
        </el-col>
        <el-col :span="4">
          <el-select v-model="searchParams.status" placeholder="状态" clearable>
            <el-option label="已提交" value="submitted" />
            <el-option label="已审核" value="reviewed" />
            <el-option label="通过" value="approved" />
            <el-option label="未通过" value="rejected" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-button type="primary" @click="fetchRecords">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-col>
      </el-row>
    </div>
    
    <el-card>
      <!-- 记录列表 -->
      <el-table
        :data="records"
        v-loading="loading"
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="student_no" label="学号" width="120" />
        <el-table-column prop="student_name" label="学生姓名" width="100" />
        <el-table-column prop="experiment_name" label="实验名称" width="200" />
        <el-table-column prop="experiment_code" label="实验代码" width="120" />
        <el-table-column prop="submitted_at" label="提交时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.submitted_at) }}
          </template>
        </el-table-column>
        <el-table-column prop="score" label="得分" width="80" />
        <el-table-column prop="is_passed" label="是否通过" width="100">
          <template #default="{ row }">
            <el-tag 
              v-if="row.is_passed !== null"
              :type="row.is_passed ? 'success' : 'danger'" 
              size="small"
            >
              {{ row.is_passed ? '通过' : '未通过' }}
            </el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)" size="small">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template #default="{ row }">
            <el-button size="small" @click="viewRecord(row)">查看</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="totalRecords"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { adminApi } from '@/api/admin'
import type { AdminRecordListParams } from '@/api/admin'

// 响应式数据
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(20)
const totalRecords = ref(0)

const records = ref<any[]>([])

const searchParams = reactive<AdminRecordListParams>({
  page: 1,
  page_size: 20,
  student_no: '',
  student_name: '',
  experiment_code: '',
  status: ''
})

// 获取记录列表
const fetchRecords = async () => {
  loading.value = true
  try {
    const params = {
      ...searchParams,
      page: currentPage.value,
      page_size: pageSize.value
    }
    
    // 清理空参数
    Object.keys(params).forEach(key => {
      if (params[key as keyof typeof params] === '') {
        delete params[key as keyof typeof params]
      }
    })
    
    const res = await adminApi.getExperimentRecords(params)
    const data = res as any
    records.value = Array.isArray(data) ? data : (data.items || data.records || [])
    totalRecords.value = data.total || records.value.length
  } catch (error) {
    console.error('获取记录列表失败:', error)
    ElMessage.error('获取记录列表失败')
  } finally {
    loading.value = false
  }
}

// 重置搜索
const resetSearch = () => {
  searchParams.student_no = ''
  searchParams.student_name = ''
  searchParams.experiment_code = ''
  searchParams.status = ''
  currentPage.value = 1
  fetchRecords()
}

// 查看记录详情
const viewRecord = (record: any) => {
  ElMessage.info('查看详情功能开发中')
}

// 获取状态类型
const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    submitted: 'info',
    reviewed: 'warning',
    approved: 'success',
    rejected: 'danger'
  }
  return statusMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    submitted: '已提交',
    reviewed: '已审核',
    approved: '通过',
    rejected: '未通过'
  }
  return statusMap[status] || status
}

// 格式化日期
const formatDate = (dateStr: string | null) => {
  if (!dateStr) return '-'
  return new Date(dateStr).toLocaleString('zh-CN')
}

// 分页处理
const handleSizeChange = (size: number) => {
  pageSize.value = size
  searchParams.page_size = size
  fetchRecords()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  searchParams.page = page
  fetchRecords()
}

// 组件挂载
onMounted(() => {
  fetchRecords()
})
</script>

<style scoped>
.admin-records {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.search-bar {
  margin-bottom: 20px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}
</style>
