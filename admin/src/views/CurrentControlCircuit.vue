<template>
  <div class="current-control-circuit">
    <div class="page-header">
      <h2>制流电路实验结果</h2>
      <div class="header-actions">
        <el-input
          v-model="studentIdFilter"
          placeholder="输入学号筛选"
          style="width: 200px; margin-right: 10px"
          clearable
          @input="fetchResults"
        />
        <el-button type="primary" @click="fetchResults">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>
    
    <el-card>
      <!-- 实验结果列表 -->
      <el-table
        :data="results"
        v-loading="loading"
        style="width: 100%"
      >
        <el-table-column prop="student_name" label="学生姓名" width="120" />
        <el-table-column prop="student_id" label="学号" width="120" />
        <el-table-column prop="submitted_at" label="提交时间" width="180">
          <template #default="{ row }">
            {{ new Date(row.submitted_at).toLocaleString() }}
          </template>
        </el-table-column>
        <el-table-column prop="is_passed" label="通过状态" width="100">
          <template #default="{ row }">
            <el-tag v-if="row.is_passed !== null" :type="row.is_passed ? 'success' : 'danger'">
              {{ row.is_passed ? '通过' : '未通过' }}
            </el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="score" label="分数" width="80">
          <template #default="{ row }">
            {{ row.score !== null ? row.score : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="calculated_resistance" label="计算电阻(Ω)" width="120">
          <template #default="{ row }">
            {{ row.calculated_resistance !== null ? row.calculated_resistance.toFixed(2) : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="error_percentage" label="误差(%)" width="100">
          <template #default="{ row }">
            {{ row.error_percentage !== null ? row.error_percentage.toFixed(2) + '%' : '-' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template #default="{ row }">
            <el-button size="small" type="primary" @click="viewDetail(row)">
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      title="制流电路实验详情"
      width="1200px"
      :close-on-click-modal="false"
    >
      <div v-if="selectedResult" class="experiment-detail">
        <!-- 基本信息 -->
        <el-card class="detail-section">
          <template #header>
            <span>基本信息</span>
          </template>
          <el-descriptions :column="3" border>
            <el-descriptions-item label="学生姓名">{{ selectedResult.student_name }}</el-descriptions-item>
            <el-descriptions-item label="学号">{{ selectedResult.student_id }}</el-descriptions-item>
            <el-descriptions-item label="提交时间">{{ new Date(selectedResult.submitted_at).toLocaleString() }}</el-descriptions-item>
            <el-descriptions-item label="通过状态">
              <el-tag v-if="selectedResult.is_passed !== null" :type="selectedResult.is_passed ? 'success' : 'danger'">
                {{ selectedResult.is_passed ? '通过' : '未通过' }}
              </el-tag>
              <span v-else>-</span>
            </el-descriptions-item>
            <el-descriptions-item label="分数">{{ selectedResult.score !== null ? selectedResult.score : '-' }}</el-descriptions-item>
            <el-descriptions-item label="环境温度">{{ selectedResult.temperature !== null ? selectedResult.temperature + '°C' : '-' }}</el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 实验数据 -->
        <el-card class="detail-section">
          <template #header>
            <span>实验数据</span>
          </template>

          <!-- 制流电路数据表格 -->
          <div class="circuit-data-section">
            <h4>制流电路数据</h4>
            <el-tabs v-model="activeTab" class="circuit-tabs">
              <!-- k=1电路数据 -->
              <el-tab-pane label="k=1电路" name="k1">
                <el-table
                  :data="getCircuitData('k1')"
                  border
                  style="width: 100%; margin-bottom: 20px"
                  size="small"
                >
                  <el-table-column prop="index" label="序号" width="80" align="center" />
                  <el-table-column prop="voltage" label="电压 (V)" width="120" align="center">
                    <template #default="{ row }">
                      {{ row.voltage?.toFixed(3) || '-' }}
                    </template>
                  </el-table-column>
                  <el-table-column prop="current" label="电流 (A)" width="120" align="center">
                    <template #default="{ row }">
                      {{ row.current?.toFixed(6) || '-' }}
                    </template>
                  </el-table-column>
                  <el-table-column prop="resistance" label="电阻 (Ω)" width="120" align="center">
                    <template #default="{ row }">
                      {{ row.resistance?.toFixed(2) || '-' }}
                    </template>
                  </el-table-column>
                </el-table>
              </el-tab-pane>

              <!-- k=0.1电路数据 -->
              <el-tab-pane label="k=0.1电路" name="k01">
                <el-table
                  :data="getCircuitData('k01')"
                  border
                  style="width: 100%; margin-bottom: 20px"
                  size="small"
                >
                  <el-table-column prop="index" label="序号" width="80" align="center" />
                  <el-table-column prop="voltage" label="电压 (V)" width="120" align="center">
                    <template #default="{ row }">
                      {{ row.voltage?.toFixed(3) || '-' }}
                    </template>
                  </el-table-column>
                  <el-table-column prop="current" label="电流 (A)" width="120" align="center">
                    <template #default="{ row }">
                      {{ row.current?.toFixed(6) || '-' }}
                    </template>
                  </el-table-column>
                  <el-table-column prop="resistance" label="电阻 (Ω)" width="120" align="center">
                    <template #default="{ row }">
                      {{ row.resistance?.toFixed(2) || '-' }}
                    </template>
                  </el-table-column>
                </el-table>
              </el-tab-pane>
            </el-tabs>
          </div>

          <!-- 原始数据概览 -->
          <el-row :gutter="20" style="margin-top: 20px">
            <el-col :span="8">
              <h4>电压数据总览 (V)</h4>
              <div class="data-summary">
                总数据点: {{ selectedResult.voltage_data?.length || 0 }}
              </div>
            </el-col>
            <el-col :span="8">
              <h4>电流数据总览 (A)</h4>
              <div class="data-summary">
                总数据点: {{ selectedResult.current_data?.length || 0 }}
              </div>
            </el-col>
            <el-col :span="8">
              <h4>电阻值总览 (Ω)</h4>
              <div class="data-summary">
                总数据点: {{ selectedResult.resistance_values?.length || 0 }}
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- 计算结果 -->
        <el-card class="detail-section">
          <template #header>
            <span>计算结果</span>
          </template>
          <el-descriptions :column="3" border>
            <el-descriptions-item label="计算电阻">
              {{ selectedResult.calculated_resistance !== null ? selectedResult.calculated_resistance.toFixed(4) + ' Ω' : '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="理论电阻">
              {{ selectedResult.theoretical_resistance !== null ? selectedResult.theoretical_resistance.toFixed(4) + ' Ω' : '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="误差百分比">
              {{ selectedResult.error_percentage !== null ? selectedResult.error_percentage.toFixed(2) + '%' : '-' }}
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 图形显示 -->
        <el-card v-if="selectedResult.plot_data" class="detail-section">
          <template #header>
            <span>实验图形</span>
          </template>
          <div class="plot-container">
            <img
              v-if="isBase64Image(selectedResult.plot_data)"
              :src="'data:image/png;base64,' + selectedResult.plot_data"
              alt="实验图形"
              style="max-width: 100%; height: auto; border: 1px solid #ddd; border-radius: 4px;"
            />
            <div v-else v-html="selectedResult.plot_data"></div>
          </div>
        </el-card>

        <!-- 分析结果 -->
        <el-card v-if="selectedResult.analysis_result" class="detail-section">
          <template #header>
            <span>分析结果</span>
          </template>
          <div class="analysis-content">
            <pre>{{ selectedResult.analysis_result }}</pre>
          </div>
        </el-card>

        <!-- 结论 -->
        <el-card v-if="selectedResult.conclusions && selectedResult.conclusions.length > 0" class="detail-section">
          <template #header>
            <span>实验结论</span>
          </template>
          <ul class="conclusions-list">
            <li v-for="(conclusion, index) in selectedResult.conclusions" :key="index">
              {{ conclusion }}
            </li>
          </ul>
        </el-card>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showDetailDialog = false">关闭</el-button>
          <el-button type="primary" @click="exportResult">导出结果</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh } from '@element-plus/icons-vue'
import { adminApi } from '@/api/admin'

// 响应式数据
const loading = ref(false)
const studentIdFilter = ref('')
const showDetailDialog = ref(false)
const activeTab = ref('k1')

const results = ref<any[]>([])
const selectedResult = ref<any>(null)

// 获取实验结果列表
const fetchResults = async () => {
  try {
    loading.value = true
    const response = await adminApi.getCurrentControlCircuitResults(
      studentIdFilter.value || undefined
    )
    results.value = response
  } catch (error: any) {
    ElMessage.error(error.response?.data?.detail || '获取实验结果失败')
  } finally {
    loading.value = false
  }
}

// 查看详情
const viewDetail = async (result: any) => {
  try {
    const response = await adminApi.getCurrentControlCircuitDetail(result.id)
    selectedResult.value = response
    showDetailDialog.value = true
  } catch (error: any) {
    ElMessage.error(error.response?.data?.detail || '获取实验详情失败')
  }
}

// 导出结果
const exportResult = () => {
  ElMessage.info('导出功能开发中')
}

// 获取电路数据
const getCircuitData = (circuitType: 'k1' | 'k01') => {
  if (!selectedResult.value) return []

  try {
    // 尝试解析JSON数据
    let circuitData = null

    // 检查是否有存储的JSON数据
    if (selectedResult.value.plot_json) {
      const jsonData = JSON.parse(selectedResult.value.plot_json)
      circuitData = jsonData[circuitType]
    }

    // 如果没有JSON数据，尝试从原始数据构造
    if (!circuitData && selectedResult.value.voltage_data && selectedResult.value.current_data) {
      const voltages = selectedResult.value.voltage_data
      const currents = selectedResult.value.current_data
      const resistances = selectedResult.value.resistance_values || []

      // 假设前半部分是k=1，后半部分是k=0.1
      const halfLength = Math.floor(voltages.length / 2)

      if (circuitType === 'k1') {
        circuitData = voltages.slice(0, halfLength).map((voltage: number, index: number) => ({
          index: index + 1,
          voltage: voltage,
          current: currents[index] || null,
          resistance: resistances[index] || (voltage && currents[index] ? voltage / currents[index] : null)
        }))
      } else {
        circuitData = voltages.slice(halfLength).map((voltage: number, index: number) => ({
          index: index + 1,
          voltage: voltage,
          current: currents[halfLength + index] || null,
          resistance: resistances[halfLength + index] || (voltage && currents[halfLength + index] ? voltage / currents[halfLength + index] : null)
        }))
      }
    }

    return circuitData || []
  } catch (error) {
    console.error('解析电路数据失败:', error)
    return []
  }
}

// 检查是否为base64图像
const isBase64Image = (data: string) => {
  if (!data) return false
  // 检查是否为base64编码的图片数据
  return /^[A-Za-z0-9+/]+=*$/.test(data) && data.length > 100
}

// 组件挂载
onMounted(() => {
  fetchResults()
})
</script>

<style scoped>
.current-control-circuit {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.header-actions {
  display: flex;
  align-items: center;
}

.detail-section {
  margin-bottom: 20px;
}

.data-list {
  max-height: 200px;
  overflow-y: auto;
}

.data-tag {
  margin: 2px;
  display: inline-block;
}

.plot-container {
  text-align: center;
  padding: 20px;
}

.analysis-content pre {
  white-space: pre-wrap;
  word-wrap: break-word;
  background-color: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
}

.conclusions-list {
  padding-left: 20px;
}

.conclusions-list li {
  margin-bottom: 8px;
  line-height: 1.6;
}

.circuit-data-section {
  margin-bottom: 20px;
}

.circuit-data-section h4 {
  margin-bottom: 15px;
  color: #303133;
  font-weight: 600;
}

.circuit-tabs {
  margin-top: 10px;
}

.data-summary {
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
  font-size: 14px;
  color: #606266;
}
</style>
