<template>
  <div class="admin-classes">
    <div class="page-header">
      <h2>班级管理</h2>
      <div class="header-actions">
        <el-button type="primary" @click="showAddDialog = true">
          <el-icon><Plus /></el-icon>
          添加班级
        </el-button>
      </div>
    </div>
    
    <!-- 搜索栏 -->
    <div class="search-bar">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-input
            v-model="searchQuery"
            placeholder="搜索班级名称或代码"
            :prefix-icon="Search"
            clearable
          />
        </el-col>
      </el-row>
    </div>
    
    <el-card>
      <!-- 班级列表 -->
      <el-table
        :data="filteredClasses"
        v-loading="loading"
        style="width: 100%"
      >
        <el-table-column prop="name" label="班级名称" width="200" />
        <el-table-column prop="code" label="班级代码" width="150" />
        <el-table-column prop="department" label="院系" width="200" />
        <el-table-column prop="grade" label="年级" width="100" />
        <el-table-column prop="student_count" label="学生数" width="100" />
        <el-table-column label="操作" width="150">
          <template #default="{ row }">
            <el-button size="small" @click="editClass(row)">编辑</el-button>
            <el-button size="small" type="danger" @click="confirmDeleteClass(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="totalClasses"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 添加/编辑班级对话框 -->
    <el-dialog
      v-model="showAddDialog"
      :title="editingClass ? '编辑班级' : '添加班级'"
      width="500px"
    >
      <el-form
        ref="classFormRef"
        :model="classForm"
        :rules="classRules"
        label-width="80px"
      >
        <el-form-item label="班级名称" prop="name">
          <el-input v-model="classForm.name" placeholder="请输入班级名称" />
        </el-form-item>
        <el-form-item label="班级代码" prop="code">
          <el-input v-model="classForm.code" placeholder="请输入班级代码" />
        </el-form-item>
        <el-form-item label="院系" prop="department">
          <el-input v-model="classForm.department" placeholder="请输入院系" />
        </el-form-item>
        <el-form-item label="年级" prop="grade">
          <el-input-number v-model="classForm.grade" :min="2020" :max="2030" placeholder="请输入年级" />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="resetForm">取消</el-button>
          <el-button type="primary" :loading="saving" @click="saveClass">
            {{ editingClass ? '更新' : '创建' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search } from '@element-plus/icons-vue'
import { adminApi } from '@/api/admin'

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const searchQuery = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const totalClasses = ref(0)
const showAddDialog = ref(false)
const editingClass = ref<any>(null)

const classes = ref<any[]>([])

// 表单数据
const classForm = ref({
  name: '',
  code: '',
  department: '',
  grade: null as number | null
})

const classFormRef = ref()

// 表单验证规则
const classRules = {
  name: [
    { required: true, message: '请输入班级名称', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入班级代码', trigger: 'blur' }
  ]
}

// 计算属性
const filteredClasses = computed(() => {
  let filtered = classes.value

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(cls =>
      cls.name.toLowerCase().includes(query) ||
      cls.code.toLowerCase().includes(query)
    )
  }

  return filtered
})

// 获取班级列表
const fetchClasses = async () => {
  loading.value = true
  try {
    const res = await adminApi.getClasses()
    classes.value = (res as any) as any[]
    totalClasses.value = classes.value.length
  } catch (error) {
    console.error('获取班级列表失败:', error)
    ElMessage.error('获取班级列表失败')
  } finally {
    loading.value = false
  }
}

// 编辑班级
const editClass = (cls: any) => {
  editingClass.value = cls
  classForm.value = {
    name: cls.name,
    code: cls.code,
    department: cls.department || '',
    grade: cls.grade
  }
  showAddDialog.value = true
}

// 确认删除班级
const confirmDeleteClass = async (cls: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除班级 "${cls.name}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await deleteClass(cls)
  } catch (error) {
    // 用户取消删除
  }
}

// 删除班级
const deleteClass = async (cls: any) => {
  try {
    await adminApi.deleteClass(cls.id)
    ElMessage.success('班级删除成功')
    fetchClasses()
  } catch (error: any) {
    ElMessage.error(error.response?.data?.detail || '删除班级失败')
  }
}

// 保存班级
const saveClass = async () => {
  if (!classFormRef.value) return

  try {
    await classFormRef.value.validate()

    saving.value = true

    if (editingClass.value) {
      // 更新班级
      await adminApi.updateClass(editingClass.value.id, classForm.value)
      ElMessage.success('班级更新成功')
    } else {
      // 创建班级
      await adminApi.createClass(classForm.value)
      ElMessage.success('班级创建成功')
    }

    showAddDialog.value = false
    resetForm()
    fetchClasses()
  } catch (error: any) {
    ElMessage.error(error.response?.data?.detail || '保存班级失败')
  } finally {
    saving.value = false
  }
}

// 重置表单
const resetForm = () => {
  showAddDialog.value = false
  editingClass.value = null
  classForm.value = {
    name: '',
    code: '',
    department: '',
    grade: null
  }
  if (classFormRef.value) {
    classFormRef.value.resetFields()
  }
}

// 分页处理
const handleSizeChange = (size: number) => {
  pageSize.value = size
  fetchClasses()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  fetchClasses()
}

// 组件挂载
onMounted(() => {
  fetchClasses()
})
</script>

<style scoped>
.admin-classes {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.search-bar {
  margin-bottom: 20px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}
</style>
