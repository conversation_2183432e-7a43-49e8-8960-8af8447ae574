<template>
  <div class="admin-experiments">
    <div class="page-header">
      <h2>实验管理</h2>
      <div class="header-actions">
        <el-button type="primary">
          <el-icon><Plus /></el-icon>
          添加实验
        </el-button>
      </div>
    </div>
    
    <!-- 搜索栏 -->
    <div class="search-bar">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-input
            v-model="searchQuery"
            placeholder="搜索实验名称或代码"
            :prefix-icon="Search"
            clearable
          />
        </el-col>
        <el-col :span="4">
          <el-select v-model="statusFilter" placeholder="选择状态" clearable>
            <el-option label="启用" value="active" />
            <el-option label="禁用" value="inactive" />
          </el-select>
        </el-col>
      </el-row>
    </div>
    
    <el-card>
      <!-- 实验列表 -->
      <el-table
        :data="filteredExperiments"
        v-loading="loading"
        style="width: 100%"
      >
        <el-table-column prop="name" label="实验名称" width="200" />
        <el-table-column prop="code" label="实验代码" width="150" />
        <el-table-column prop="description" label="描述" show-overflow-tooltip />
        <el-table-column prop="duration_minutes" label="时长(分钟)" width="100" />
        <el-table-column prop="max_score" label="满分" width="80" />
        <el-table-column prop="is_active" label="状态" width="80">
          <template #default="{ row }">
            <el-tag :type="row.is_active ? 'success' : 'danger'" size="small">
              {{ row.is_active ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <el-button size="small" @click="editExperiment(row)">编辑</el-button>
            <el-button
              size="small"
              :type="row.is_active ? 'warning' : 'success'"
              @click="toggleExperimentStatus(row)"
            >
              {{ row.is_active ? '禁用' : '启用' }}
            </el-button>
            <el-button size="small" type="danger" @click="deleteExperiment(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="totalExperiments"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, Search } from '@element-plus/icons-vue'
import { adminApi } from '@/api/admin'

// 响应式数据
const loading = ref(false)
const searchQuery = ref('')
const statusFilter = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const totalExperiments = ref(0)

const experiments = ref<any[]>([])

// 计算属性
const filteredExperiments = computed(() => {
  let filtered = experiments.value

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(exp => 
      exp.name.toLowerCase().includes(query) ||
      exp.code.toLowerCase().includes(query)
    )
  }

  if (statusFilter.value) {
    const isActive = statusFilter.value === 'active'
    filtered = filtered.filter(exp => exp.is_active === isActive)
  }

  return filtered
})

// 获取实验列表
const fetchExperiments = async () => {
  loading.value = true
  try {
    const res = await adminApi.getExperimentTypes()
    experiments.value = (res as any) as any[]
    totalExperiments.value = experiments.value.length
  } catch (error) {
    console.error('获取实验列表失败:', error)
    ElMessage.error('获取实验列表失败')
  } finally {
    loading.value = false
  }
}

// 编辑实验
const editExperiment = (experiment: any) => {
  ElMessage.info('编辑功能开发中')
}

// 切换实验状态
const toggleExperimentStatus = (experiment: any) => {
  ElMessage.info('状态切换功能开发中')
}

// 删除实验
const deleteExperiment = (experiment: any) => {
  ElMessage.info('删除功能开发中')
}

// 分页处理
const handleSizeChange = (size: number) => {
  pageSize.value = size
  fetchExperiments()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  fetchExperiments()
}

// 组件挂载
onMounted(() => {
  fetchExperiments()
})
</script>

<style scoped>
.admin-experiments {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.search-bar {
  margin-bottom: 20px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}
</style>
