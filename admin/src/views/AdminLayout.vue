<template>
  <div class="admin-layout">
    <!-- 顶部导航 -->
    <div class="admin-header">
      <div class="header-left">
        <h1>大学物理实验管理后台</h1>
      </div>
      <div class="header-right">
        <span class="welcome-text">管理员后台</span>
      </div>
    </div>
    
    <!-- 主体内容 -->
    <div class="admin-content">
      <el-container>
        <!-- 侧边栏 -->
        <el-aside width="250px" class="admin-sidebar">
          <el-menu
            :default-active="$route.path"
            class="admin-menu"
            router
            unique-opened
          >
            <el-menu-item index="/admin/dashboard">
              <el-icon><DataBoard /></el-icon>
              <span>仪表盘</span>
            </el-menu-item>
            
            <el-menu-item index="/admin/students">
              <el-icon><User /></el-icon>
              <span>学生管理</span>
            </el-menu-item>
            
            <el-menu-item index="/admin/classes">
              <el-icon><School /></el-icon>
              <span>班级管理</span>
            </el-menu-item>
            
            <el-menu-item index="/admin/experiments">
              <el-icon><Histogram /></el-icon>
              <span>实验管理</span>
            </el-menu-item>
            
            <el-menu-item index="/admin/learning-stats">
              <el-icon><TrendCharts /></el-icon>
              <span>学习统计</span>
            </el-menu-item>
            
            <el-menu-item index="/admin/records">
              <el-icon><Document /></el-icon>
              <span>实验记录</span>
            </el-menu-item>

            <el-menu-item index="/admin/current-control-circuit">
              <el-icon><Connection /></el-icon>
              <span>制流电路实验</span>
            </el-menu-item>
          </el-menu>
        </el-aside>
        
        <!-- 主内容区 -->
        <el-main class="admin-main">
          <div class="page-content">
            <router-view />
          </div>
        </el-main>
      </el-container>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  DataBoard,
  User,
  School,
  Histogram,
  TrendCharts,
  Document,
  Connection
} from '@element-plus/icons-vue'
</script>

<style scoped>
.admin-layout {
  min-height: 100vh;
  background-color: #f5f7fa;
}

.admin-header {
  background: white;
  padding: 0 30px;
  height: 60px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: sticky;
  top: 0;
  z-index: 1000;
}

.header-left h1 {
  margin: 0;
  color: #303133;
  font-size: 1.5rem;
  font-weight: 600;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 15px;
}

.welcome-text {
  color: #606266;
  font-size: 14px;
}

.admin-content {
  height: calc(100vh - 60px);
}

.admin-sidebar {
  background: white;
  border-right: 1px solid #e4e7ed;
}

.admin-menu {
  height: 100%;
  border-right: none;
}

.admin-main {
  padding: 0;
  background: #f5f7fa;
}

.page-content {
  padding: 20px;
  min-height: 100%;
}

:deep(.el-menu-item) {
  height: 50px;
  line-height: 50px;
  margin: 4px 8px;
  border-radius: 6px;
}

:deep(.el-menu-item:hover) {
  background-color: #ecf5ff;
  color: #409eff;
}

:deep(.el-menu-item.is-active) {
  background-color: #409eff;
  color: white;
}

:deep(.el-menu-item.is-active .el-icon) {
  color: white;
}
</style>
