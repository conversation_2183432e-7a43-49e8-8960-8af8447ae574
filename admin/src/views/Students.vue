<template>
  <div class="admin-students">
    <div class="page-header">
      <h2>学生管理</h2>
      <div class="header-actions">
        <el-button type="primary" @click="showAddDialog = true">
          <el-icon><Plus /></el-icon>
          添加学生
        </el-button>
        <el-button type="success" @click="showImportDialog = true">
          <el-icon><Upload /></el-icon>
          批量导入
        </el-button>
      </div>
    </div>
    
    <!-- 搜索和筛选 -->
    <div class="search-bar">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-input
            v-model="searchQuery"
            placeholder="搜索学生姓名或学号"
            :prefix-icon="Search"
            clearable
            @input="handleSearch"
          />
        </el-col>
        <el-col :span="4">
          <el-select v-model="classFilter" placeholder="选择班级" clearable @change="fetchStudents">
            <el-option
              v-for="cls in classes"
              :key="cls.id"
              :label="cls.name"
              :value="cls.id"
            />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select v-model="statusFilter" placeholder="选择状态" clearable>
            <el-option label="激活" value="active" />
            <el-option label="禁用" value="inactive" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-checkbox v-model="includeUnassigned" @change="fetchStudents">
            显示无班级学生
          </el-checkbox>
        </el-col>
      </el-row>
    </div>
    
    <el-card>
      <!-- 学生列表 -->
      <el-table
        :data="filteredStudents"
        v-loading="loading"
        style="width: 100%"
      >
        <el-table-column prop="student_id" label="学号" width="120" />
        <el-table-column prop="name" label="姓名" width="100" />
        <el-table-column prop="class_name" label="班级" width="120" />
        <el-table-column prop="group_name" label="分组" width="100" />
        <el-table-column prop="email" label="邮箱" />
        <el-table-column prop="experiment_count" label="实验数" width="80" />
        <el-table-column prop="is_active" label="状态" width="80">
          <template #default="{ row }">
            <el-tag :type="row.is_active ? 'success' : 'danger'" size="small">
              {{ row.is_active ? '激活' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="250">
          <template #default="{ row }">
            <el-button size="small" @click="editStudent(row)">编辑</el-button>
            <el-button size="small" type="info" @click="viewStudentDetail(row)">
              详情
            </el-button>
            <el-button size="small" type="success" @click="viewStudentRecords(row)">
              实验记录
            </el-button>
            <el-button
              size="small"
              :type="row.is_active ? 'warning' : 'success'"
              @click="toggleStudentStatus(row)"
            >
              {{ row.is_active ? '禁用' : '启用' }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="totalStudents"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 添加学生对话框 -->
    <el-dialog
      v-model="showAddDialog"
      title="添加学生"
      width="500px"
    >
      <el-form
        ref="studentFormRef"
        :model="studentForm"
        :rules="studentRules"
        label-width="80px"
      >
        <el-form-item label="学号" prop="student_id">
          <el-input v-model="studentForm.student_id" placeholder="请输入学号" />
        </el-form-item>
        <el-form-item label="姓名" prop="name">
          <el-input v-model="studentForm.name" placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="班级" prop="class_id">
          <el-select v-model="studentForm.class_id" placeholder="选择班级" style="width: 100%">
            <el-option
              v-for="cls in classes"
              :key="cls.id"
              :label="cls.name"
              :value="cls.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="studentForm.email" placeholder="请输入邮箱" />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="resetStudentForm">取消</el-button>
          <el-button type="primary" :loading="saving" @click="saveStudent">
            创建
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 批量导入对话框 -->
    <el-dialog
      v-model="showImportDialog"
      title="批量导入学生"
      width="600px"
    >
      <div class="import-content">
        <el-alert
          title="导入说明"
          type="info"
          :closable="false"
          style="margin-bottom: 20px"
        >
          <p>请上传CSV格式文件，文件应包含以下列：</p>
          <ul>
            <li><strong>student_id</strong>: 学号（必填）</li>
            <li><strong>name</strong>: 姓名（必填）</li>
            <li><strong>class_code</strong>: 班级代码（必填）</li>
            <li><strong>email</strong>: 邮箱（可选）</li>
            <li><strong>group_name</strong>: 分组名称（可选）</li>
          </ul>
        </el-alert>

        <el-upload
          ref="uploadRef"
          :auto-upload="false"
          :limit="1"
          accept=".csv"
          :on-change="handleFileChange"
          :file-list="fileList"
          drag
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">
            将CSV文件拖到此处，或<em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              只能上传CSV文件，且不超过2MB
            </div>
          </template>
        </el-upload>

        <div v-if="importResult" class="import-result" style="margin-top: 20px">
          <el-alert
            :title="`导入完成：成功 ${importResult.success_count} 条，失败 ${importResult.error_count} 条`"
            :type="importResult.error_count > 0 ? 'warning' : 'success'"
            :closable="false"
          >
            <div v-if="importResult.errors.length > 0">
              <p>错误详情：</p>
              <ul>
                <li v-for="error in importResult.errors" :key="error">{{ error }}</li>
              </ul>
            </div>
          </el-alert>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="resetImportDialog">取消</el-button>
          <el-button type="primary" :loading="importing" @click="importStudents" :disabled="!selectedFile">
            导入
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 学生详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      title="学生详情"
      width="600px"
    >
      <div v-if="selectedStudent" class="student-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="学号">{{ selectedStudent.student_id }}</el-descriptions-item>
          <el-descriptions-item label="姓名">{{ selectedStudent.name }}</el-descriptions-item>
          <el-descriptions-item label="班级">{{ selectedStudent.class_name }}</el-descriptions-item>
          <el-descriptions-item label="分组">{{ selectedStudent.group_name || '无' }}</el-descriptions-item>
          <el-descriptions-item label="邮箱">{{ selectedStudent.email || '无' }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="selectedStudent.is_active ? 'success' : 'danger'">
              {{ selectedStudent.is_active ? '激活' : '禁用' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="实验记录数">{{ selectedStudent.experiment_count }}</el-descriptions-item>
          <el-descriptions-item label="类型">
            <el-tag :type="selectedStudent.is_unassigned ? 'warning' : 'primary'">
              {{ selectedStudent.is_unassigned ? '未分班' : '正式学生' }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showDetailDialog = false">关闭</el-button>
          <el-button type="primary" @click="viewStudentRecords(selectedStudent)">
            查看实验记录
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 学生实验记录对话框 -->
    <el-dialog
      v-model="showRecordsDialog"
      title="学生实验记录"
      width="1000px"
    >
      <div v-if="selectedStudent" class="records-header">
        <h4>{{ selectedStudent.name }}（{{ selectedStudent.student_id }}）的实验记录</h4>
        <el-select v-model="recordsFilter" placeholder="选择实验类型" clearable @change="fetchStudentRecords">
          <el-option label="全部实验" value="" />
          <el-option label="制流电路实验" value="current_control_circuit" />
          <el-option label="示波器实验" value="oscilloscope" />
        </el-select>
      </div>

      <el-table
        :data="studentRecords"
        v-loading="recordsLoading"
        style="width: 100%; margin-top: 20px"
      >
        <el-table-column prop="experiment_name" label="实验名称" width="150" />
        <el-table-column prop="submitted_at" label="提交时间" width="180">
          <template #default="{ row }">
            {{ new Date(row.submitted_at).toLocaleString() }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status === 'submitted' ? 'success' : 'info'">
              {{ row.status === 'submitted' ? '已提交' : row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="is_passed" label="通过状态" width="100">
          <template #default="{ row }">
            <el-tag v-if="row.is_passed !== null" :type="row.is_passed ? 'success' : 'danger'">
              {{ row.is_passed ? '通过' : '未通过' }}
            </el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="score" label="分数" width="80">
          <template #default="{ row }">
            {{ row.score !== null ? row.score : '-' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150">
          <template #default="{ row }">
            <el-button size="small" @click="viewRecordDetail(row)">查看详情</el-button>
            <el-button v-if="row.experiment_code === 'current_control_circuit'"
                      size="small" type="primary" @click="viewCircuitResult(row)">
              查看结果
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showRecordsDialog = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, Search, Upload, UploadFilled } from '@element-plus/icons-vue'
import { adminApi } from '@/api/admin'

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const importing = ref(false)
const searchQuery = ref('')
const classFilter = ref<number | null>(null)
const statusFilter = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const totalStudents = ref(0)
const showAddDialog = ref(false)
const showImportDialog = ref(false)
const showDetailDialog = ref(false)
const showRecordsDialog = ref(false)
const includeUnassigned = ref(false)
const recordsLoading = ref(false)
const recordsFilter = ref('')

const students = ref<any[]>([])
const classes = ref<any[]>([])
const selectedStudent = ref<any>(null)
const studentRecords = ref<any[]>([])

// 表单数据
const studentForm = ref({
  student_id: '',
  name: '',
  class_id: null as number | null,
  email: ''
})

const studentFormRef = ref()

// 表单验证规则
const studentRules = {
  student_id: [
    { required: true, message: '请输入学号', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' }
  ],
  class_id: [
    { required: true, message: '请选择班级', trigger: 'change' }
  ]
}

// 导入相关
const uploadRef = ref()
const fileList = ref([])
const selectedFile = ref<File | null>(null)
const importResult = ref<any>(null)

// 计算属性
const filteredStudents = computed(() => {
  let filtered = students.value

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(student => 
      student.name.toLowerCase().includes(query) ||
      student.student_id.toLowerCase().includes(query)
    )
  }

  if (statusFilter.value) {
    const isActive = statusFilter.value === 'active'
    filtered = filtered.filter(student => student.is_active === isActive)
  }

  return filtered
})

// 获取学生列表
const fetchStudents = async () => {
  loading.value = true
  try {
    const params: any = {}
    if (classFilter.value) {
      params.class_id = classFilter.value
    }

    if (includeUnassigned.value) {
      params.include_unassigned = true
    }

    const res = await adminApi.getStudents(params)
    students.value = (res as any) as any[]
    totalStudents.value = students.value.length
  } catch (error) {
    console.error('获取学生列表失败:', error)
    ElMessage.error('获取学生列表失败')
  } finally {
    loading.value = false
  }
}

// 获取班级列表
const fetchClasses = async () => {
  try {
    const res = await adminApi.getClasses()
    classes.value = (res as any) as any[]
  } catch (error) {
    console.error('获取班级列表失败:', error)
  }
}

// 搜索处理
const handleSearch = () => {
  // 搜索逻辑已在计算属性中处理
}

// 编辑学生
const editStudent = (student: any) => {
  ElMessage.info('编辑功能开发中')
}

// 查看学生详情
const viewStudentDetail = async (student: any) => {
  try {
    const response = await adminApi.getStudentDetail(student.id)
    selectedStudent.value = response
    showDetailDialog.value = true
  } catch (error: any) {
    ElMessage.error(error.response?.data?.detail || '获取学生详情失败')
  }
}

// 查看学生实验记录
const viewStudentRecords = async (student: any) => {
  selectedStudent.value = student
  showRecordsDialog.value = true
  await fetchStudentRecords()
}

// 获取学生实验记录
const fetchStudentRecords = async () => {
  if (!selectedStudent.value) return

  try {
    recordsLoading.value = true
    const response = await adminApi.getStudentRecords(
      selectedStudent.value.id,
      recordsFilter.value || undefined
    )
    studentRecords.value = response
  } catch (error: any) {
    ElMessage.error(error.response?.data?.detail || '获取实验记录失败')
  } finally {
    recordsLoading.value = false
  }
}

// 查看记录详情
const viewRecordDetail = (record: any) => {
  ElMessage.info('记录详情功能开发中')
}

// 查看制流电路结果
const viewCircuitResult = (record: any) => {
  ElMessage.info('制流电路结果查看功能开发中')
}

// 切换学生状态
const toggleStudentStatus = (student: any) => {
  ElMessage.info('状态切换功能开发中')
}

// 保存学生
const saveStudent = async () => {
  if (!studentFormRef.value) return

  try {
    await studentFormRef.value.validate()

    saving.value = true

    await adminApi.createStudent(studentForm.value)
    ElMessage.success('学生创建成功')

    showAddDialog.value = false
    resetStudentForm()
    fetchStudents()
  } catch (error: any) {
    ElMessage.error(error.response?.data?.detail || '创建学生失败')
  } finally {
    saving.value = false
  }
}

// 重置学生表单
const resetStudentForm = () => {
  showAddDialog.value = false
  studentForm.value = {
    student_id: '',
    name: '',
    class_id: null,
    email: ''
  }
  if (studentFormRef.value) {
    studentFormRef.value.resetFields()
  }
}

// 文件选择处理
const handleFileChange = (file: any) => {
  selectedFile.value = file.raw
  importResult.value = null
}

// 导入学生
const importStudents = async () => {
  if (!selectedFile.value) {
    ElMessage.error('请选择要导入的文件')
    return
  }

  try {
    importing.value = true
    const result = await adminApi.importStudents(selectedFile.value)
    importResult.value = result

    if (result.success_count > 0) {
      ElMessage.success(`成功导入 ${result.success_count} 名学生`)
      fetchStudents()
    }

    if (result.error_count > 0) {
      ElMessage.warning(`有 ${result.error_count} 条记录导入失败，请查看详情`)
    }
  } catch (error: any) {
    ElMessage.error(error.response?.data?.detail || '导入失败')
  } finally {
    importing.value = false
  }
}

// 重置导入对话框
const resetImportDialog = () => {
  showImportDialog.value = false
  selectedFile.value = null
  fileList.value = []
  importResult.value = null
  if (uploadRef.value) {
    uploadRef.value.clearFiles()
  }
}

// 分页处理
const handleSizeChange = (size: number) => {
  pageSize.value = size
  fetchStudents()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  fetchStudents()
}

// 组件挂载
onMounted(() => {
  fetchClasses()
  fetchStudents()
})
</script>

<style scoped>
.admin-students {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.search-bar {
  margin-bottom: 20px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}
</style>
