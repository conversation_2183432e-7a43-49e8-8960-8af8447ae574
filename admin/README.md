# 大学物理实验管理后台

这是一个独立的管理员端应用，用于管理大学物理实验系统的数据和统计。

## 功能特性

### 📊 仪表盘
- 系统概览统计（学生数、实验数、提交数、通过率）
- 最近活动记录
- 关键指标卡片展示

### 👥 学生管理
- 学生列表查看
- 按班级筛选
- 学生状态管理
- 搜索功能

### 🏫 班级管理
- 班级列表查看
- 班级信息管理
- 学生数统计

### 🧪 实验管理
- 实验类型列表
- 实验状态管理
- 实验配置

### 📈 学习统计
- **班级统计**：各班级提交数、通过数、通过率、平均分
- **分组统计**：小组学习效果对比
- **学生统计**：个人学习进度排行（前20名活跃学生）
- **实验覆盖率**：各实验的参与度和通过率分析
- **提交趋势**：7天/30天/90天的时间维度统计图表

### 📝 实验记录
- 提交记录查看
- 多维度筛选（学号、姓名、实验代码、状态）
- 记录详情查看

## 技术栈

- **前端框架**：Vue 3 + TypeScript
- **UI 组件库**：Element Plus
- **图表库**：ECharts
- **构建工具**：Vite
- **HTTP 客户端**：Axios
- **路由**：Vue Router

## 开发环境启动

### 前置条件
- Node.js 18+
- npm 或 yarn

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

访问 http://localhost:3000

### 构建生产版本
```bash
npm run build
```

### 类型检查
```bash
npm run type-check
```

## Docker 部署

### 构建镜像
```bash
docker build -t physics-admin .
```

### 运行容器
```bash
docker run -p 3000:80 physics-admin
```

### 使用 Docker Compose
在项目根目录运行：
```bash
docker compose up admin
```

## API 配置

管理员端通过以下 API 与后端通信：

- **概览统计**：`GET /api/admin/statistics/overview`
- **学习统计**：`GET /api/admin/statistics/progress`
- **提交趋势**：`GET /api/admin/statistics/trends`
- **学生列表**：`GET /api/admin/students`
- **班级列表**：`GET /api/admin/classes`
- **实验类型**：`GET /api/experiments/types`
- **实验记录**：`GET /api/admin/records`

## 环境变量

创建 `.env.local` 文件配置：

```env
# API 基础地址
VITE_API_BASE_URL=http://localhost:8000

# 应用标题
VITE_APP_TITLE=大学物理实验管理后台
```

## 项目结构

```
admin/
├── src/
│   ├── api/           # API 接口定义
│   ├── views/         # 页面组件
│   ├── router/        # 路由配置
│   ├── App.vue        # 根组件
│   └── main.ts        # 入口文件
├── public/            # 静态资源
├── Dockerfile         # Docker 构建文件
├── nginx.conf         # Nginx 配置
└── package.json       # 项目配置
```

## 特色功能

### 🎯 多维度学习统计
- 支持按班级筛选所有统计数据
- 彩色标签显示通过率等级（绿色≥80%，橙色60-79%，红色<60%）
- 覆盖率分析（绿色≥80%，橙色50-79%，红色<50%）

### 📊 可视化图表
- ECharts 集成的趋势分析图表
- 支持7天、30天、90天时间维度切换
- 提交数、通过数、通过率三线对比

### 🔍 智能筛选
- 实时搜索功能
- 多条件组合筛选
- 分页展示优化

## 注意事项

1. **无登录系统**：管理员端不需要登录，直接访问即可使用
2. **数据实时性**：所有数据都是从后端 API 实时获取
3. **响应式设计**：支持不同屏幕尺寸的设备访问
4. **错误处理**：完善的错误提示和异常处理机制

## 开发说明

- 所有 API 调用都通过 `src/api/admin.ts` 统一管理
- 使用 TypeScript 确保类型安全
- Element Plus 提供一致的 UI 体验
- 支持热重载开发模式
