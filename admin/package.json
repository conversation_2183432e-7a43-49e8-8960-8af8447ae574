{"name": "physics-experiments-admin", "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build-with-check": "vue-tsc && vite build", "preview": "vite preview", "type-check": "vue-tsc --build --force"}, "dependencies": {"vue": "^3.4.0", "vue-router": "^4.2.5", "pinia": "^2.1.7", "element-plus": "^2.4.4", "@element-plus/icons-vue": "^2.3.1", "axios": "^1.6.2", "echarts": "^5.4.3"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.0", "typescript": "^5.3.3", "vue-tsc": "^2.0.0", "vite": "^5.0.10", "@types/node": "^20.10.6"}}