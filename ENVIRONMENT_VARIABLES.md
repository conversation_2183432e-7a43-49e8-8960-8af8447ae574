# 环境变量配置规范

## 概述

本项目使用环境变量来管理所有配置，避免硬编码端口和URL。所有环境变量都定义在根目录的 `.env` 文件中。

## 端口配置

### 外部端口（宿主机端口）
这些端口用于从宿主机访问Docker容器服务：

```bash
# 30000系列端口，避免与其他应用冲突
DB_EXTERNAL_PORT=30001          # 数据库外部端口
API_EXTERNAL_PORT=30000         # 后端API外部端口  
FRONTEND_EXTERNAL_PORT=30002    # 前端应用外部端口
ADMIN_EXTERNAL_PORT=30003       # 管理后台外部端口
```

### 内部端口（容器内端口）
这些端口是容器内部使用的固定端口，**不应修改**：

- **数据库容器**: 3306 (MySQL标准端口)
- **后端容器**: 8000 (FastAPI应用端口)
- **前端容器**: 80 (nginx端口)
- **管理后台容器**: 80 (nginx端口)

## 数据库配置

```bash
DB_HOST=db                      # 容器间通信使用服务名
DB_PORT=3306                    # MySQL标准端口
DB_USER=root                    # 数据库用户名
DB_PASSWORD=example             # 数据库密码
DB_NAME=physics_experiments     # 数据库名称
DATABASE_URL=                   # 留空使用上述DB_*配置
```

## API配置

```bash
API_HOST=localhost              # API主机地址
API_PORT=${API_EXTERNAL_PORT}   # API端口（引用外部端口）
VITE_API_BASE_URL=              # 前端API基础URL（留空使用相对路径）
```

## AI服务配置

```bash
AI_ANALYSIS_ENABLED=false                          # 是否启用AI分析
AI_SERVICE_URL=http://localhost:${API_EXTERNAL_PORT}  # AI服务URL
AI_SERVICE_API_KEY=                                 # AI服务API密钥
```

## 使用规范

### 1. 在配置文件中使用环境变量

**docker-compose.yml**:
```yaml
services:
  backend:
    ports:
      - "${API_EXTERNAL_PORT}:8000"
  frontend:
    ports:
      - "${FRONTEND_EXTERNAL_PORT}:80"
```

**nginx.conf**:
```nginx
# 容器间通信使用服务名和内部端口
proxy_pass http://backend:8000/api/;
```

### 2. 在文档中使用环境变量

**正确**:
```bash
curl http://localhost:${API_EXTERNAL_PORT}/api/health
```

**错误**:
```bash
curl http://localhost:30000/api/health  # 硬编码端口
```

### 3. 在代码中使用环境变量

**前端 (vite.config.ts)**:
```typescript
const API_BASE = getApiBase()  // 从.env读取API_EXTERNAL_PORT
```

**后端 (config.py)**:
```python
API_EXTERNAL_PORT: int = int(os.getenv('API_EXTERNAL_PORT', '30000'))
```

## 端口修改指南

### 修改外部端口
只需修改 `.env` 文件中的相应变量：

```bash
# 将前端端口从30002改为30012
FRONTEND_EXTERNAL_PORT=30012
```

### 不要修改内部端口
内部端口是容器内部使用的标准端口，修改会导致服务无法启动：

```bash
# ❌ 错误：不要修改这些
DB_PORT=3306        # MySQL标准端口
# 后端容器内部固定使用8000端口
# 前端/管理后台容器内部固定使用80端口
```

## 网络架构图

```
外部访问:
用户 → localhost:${FRONTEND_EXTERNAL_PORT} → 前端容器:80
用户 → localhost:${API_EXTERNAL_PORT} → 后端容器:8000
用户 → localhost:${DB_EXTERNAL_PORT} → 数据库容器:3306

容器间通信:
前端容器:80 → backend:8000 → 后端容器:8000
后端容器:8000 → db:3306 → 数据库容器:3306
```

## 故障排除

### 端口冲突
如果遇到端口冲突，修改 `.env` 文件中的外部端口：

```bash
# 检查端口占用
netstat -tulpn | grep :30000

# 修改为其他端口
API_EXTERNAL_PORT=30100
```

### 服务无法访问
1. 检查 `.env` 文件中的端口配置
2. 确认 `docker-compose.yml` 正确引用了环境变量
3. 重启服务：`docker-compose down && docker-compose up -d`

### 容器间通信失败
1. 确认使用服务名而不是localhost
2. 确认使用内部端口而不是外部端口
3. 检查nginx配置文件中的代理设置

## 最佳实践

1. **统一使用环境变量**: 所有端口和URL都通过环境变量配置
2. **区分内外端口**: 明确区分容器内部端口和外部映射端口
3. **使用服务名**: 容器间通信使用Docker服务名
4. **文档同步**: 文档中的示例也使用环境变量格式
5. **版本控制**: `.env` 文件包含在版本控制中，但敏感信息使用 `.env.local`
