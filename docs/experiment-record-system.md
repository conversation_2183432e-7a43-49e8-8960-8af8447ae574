# 实验记录表系统使用说明

## 系统概述

实验记录表系统是一个基于JSON格式的实验数据管理系统，能够根据不同的实验类型自动解析和显示实验数据。系统采用工厂模式设计，支持多种实验类型，并且具有良好的扩展性。

## 核心特性

### 1. 多实验类型支持
- **制流电路实验**: 专门处理k=0.1和k=1的电流数据
- **示波器实验**: 处理频率、幅度、波形类型和时间-电压测量数据
- **通用实验**: 处理任意格式的实验数据

### 2. 统一的JSON格式
所有实验记录都遵循统一的JSON结构：
```json
{
  "实验数据": { /* 具体的实验测量数据 */ },
  "实验图形": "base64编码的图片数据",
  "实验分析": "AI分析结果"
}
```

### 3. 智能解析机制
- 根据实验类型代码(`experiment_code`)自动选择对应的解析器
- 数据验证和错误处理
- 解析失败时自动降级到通用格式

## 系统架构

### 核心组件

1. **ExperimentRecordTableFactory.vue** - 工厂组件
   - 根据实验类型路由到对应的记录表组件
   - 统一的数据解析入口

2. **CurrentControlCircuitRecordTable.vue** - 制流电路实验记录表
   - 显示k=1和k=0.1的电流数据表格
   - 实验环境信息展示
   - 专门的JSON格式输出

3. **OscilloscopeRecordTable.vue** - 示波器实验记录表
   - 显示基本参数（频率、幅度、波形类型）
   - 时间-电压测量数据表格
   - 波形图展示

4. **GenericExperimentRecordTable.vue** - 通用实验记录表
   - 处理未知类型或解析失败的实验
   - 通用JSON数据展示
   - 错误信息提示

### 数据解析服务

**experimentDataParser.ts** 提供以下功能：
- `parseExperimentData()` - 根据实验类型解析数据
- `parseCurrentControlCircuitData()` - 制流电路实验数据解析
- `parseOscilloscopeData()` - 示波器实验数据解析
- `createExperimentRecordJson()` - 创建完整的JSON记录

## 使用方法

### 1. 制流电路实验数据提交

```json
{
  "student_id": "2021001",
  "student_name": "张三",
  "k1Data": [
    { "ratio": 0.1, "current": 0.0012 },
    { "ratio": 0.2, "current": 0.0024 },
    { "ratio": 0.3, "current": 0.0036 }
  ],
  "k01Data": [
    { "ratio": 0.1, "current": 0.00012 },
    { "ratio": 0.2, "current": 0.00024 },
    { "ratio": 0.3, "current": 0.00036 }
  ],
  "temperature": 25.5,
  "humidity": 60,
  "notes": "实验过程正常"
}
```

### 2. 示波器实验数据提交

```json
{
  "student_id": "2021002",
  "student_name": "李四",
  "frequency": 1000,
  "amplitude": 5.0,
  "waveform_type": "正弦波",
  "measurements": [
    { "time": 0.000000, "voltage": 0.0000 },
    { "time": 0.000100, "voltage": 2.9389 },
    { "time": 0.000200, "voltage": 4.7553 }
  ],
  "notes": "波形稳定"
}
```

### 3. 在组件中使用

```vue
<template>
  <ExperimentRecordTableFactory
    :record="experimentRecord"
    :experiment-type="experimentType"
  />
</template>

<script setup>
import ExperimentRecordTableFactory from '@/components/experiment-details/ExperimentRecordTableFactory.vue'

const experimentRecord = {
  id: 1,
  submission_data: { /* 实验数据 */ },
  plot_data: "base64图片数据",
  analysis_result: "AI分析结果",
  // ... 其他字段
}

const experimentType = "current_control_circuit" // 或 "oscilloscope"
</script>
```

## 扩展新的实验类型

### 1. 定义数据接口

在 `experimentDataParser.ts` 中添加新的数据接口：

```typescript
export interface NewExperimentData {
  student_id: string
  student_name: string
  // 添加实验特定的字段
  parameter1: number
  parameter2: string
  measurements: Array<{
    // 测量数据结构
  }>
  notes?: string
}
```

### 2. 创建解析函数

```typescript
export function parseNewExperimentData(submissionData: string | object): ParsedExperimentData {
  // 实现解析逻辑
  // 包括数据验证和错误处理
}
```

### 3. 更新解析路由

在 `parseExperimentData()` 函数中添加新的case：

```typescript
export function parseExperimentData(experimentCode: string, submissionData: string | object): ParsedExperimentData {
  switch (experimentCode) {
    case 'current_control_circuit':
      return parseCurrentControlCircuitData(submissionData)
    case 'oscilloscope':
      return parseOscilloscopeData(submissionData)
    case 'new_experiment': // 新增
      return parseNewExperimentData(submissionData)
    default:
      return {
        type: 'unknown',
        data: submissionData,
        isValid: false,
        errors: [`未知的实验类型: ${experimentCode}`]
      }
  }
}
```

### 4. 创建专门的记录表组件

创建 `NewExperimentRecordTable.vue` 组件，参考现有组件的结构。

### 5. 更新工厂组件

在 `ExperimentRecordTableFactory.vue` 中添加新的条件分支：

```vue
<template>
  <div class="experiment-record-table-factory">
    <!-- 现有组件 -->
    
    <!-- 新实验记录表 -->
    <NewExperimentRecordTable
      v-else-if="experimentType === 'new_experiment' && parsedData?.isValid"
      :data="parsedData.data"
      :record="record"
    />
    
    <!-- 通用实验记录表 -->
    <GenericExperimentRecordTable
      v-else
      :record="record"
      :parsed-data="parsedData"
      :experiment-type="experimentType"
    />
  </div>
</template>
```

## 功能特性

### 1. 数据展示
- 基本信息展示（学生姓名、学号、提交时间等）
- 实验数据表格化展示
- 实验图形预览和下载
- AI分析结果展示

### 2. 数据操作
- JSON数据复制到剪贴板
- JSON文件下载
- 图片文件下载
- 大图预览

### 3. 错误处理
- 数据解析错误提示
- 缺失字段警告
- 格式验证错误显示

### 4. 响应式设计
- 移动端适配
- 折叠面板设计
- 优雅的加载状态

## 最佳实践

1. **数据验证**: 在解析函数中进行充分的数据验证
2. **错误处理**: 提供清晰的错误信息和降级方案
3. **性能优化**: 大量数据时考虑分页或虚拟滚动
4. **用户体验**: 提供直观的操作界面和反馈
5. **扩展性**: 保持代码结构的一致性，便于后续扩展

## 注意事项

1. 所有实验数据都应包含 `student_id` 和 `student_name` 字段
2. JSON数据应该是有效的格式，避免循环引用
3. 图片数据使用base64编码存储
4. 新增实验类型时要同时更新相关文档
5. 测试新功能时要考虑各种边界情况
