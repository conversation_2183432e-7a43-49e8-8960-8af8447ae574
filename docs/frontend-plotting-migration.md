# 前端绘图迁移文档

## 概述

本文档记录了将直流电路实验的绘图功能从后端迁移到前端的完整过程。

## 修改目标

1. **学生端和管理员端都使用前端Plotly绘图**
2. **前端绘图功能作为Vue模块，可在不同地方调用**
3. **数据库中直流电路实验的submission_data不再包含图片信息**
4. **图片在前端生成，提升性能和交互性**

## 主要修改

### 1. 数据库结构修改

**文件**: `database/init.sql`

- 修改 `experiment_records` 表的 `submission_data` 字段注释
- 移除 `plot_image_max_size` 系统配置项

```sql
-- 修改前
submission_data JSON NOT NULL COMMENT '提交的实验数据(包含图形数据和AI分析结果)',

-- 修改后  
submission_data JSON NOT NULL COMMENT '提交的实验数据和AI分析结果(图形在前端生成)',
```

### 2. 前端新增组件和工具

#### 新增文件

1. **`frontend/src/utils/plotlyUtils.ts`** - 绘图工具函数
   - `generateCurrentControlCircuitPlot()` - 制流电路实验绘图
   - `generateOscilloscopePlot()` - 示波器实验绘图（预留）
   - `exportPlotToPNG()` - 图表导出功能

2. **`frontend/src/components/charts/PlotlyChart.vue`** - 通用Plotly图表组件
   - 支持动态导入Plotly.js
   - 提供图表交互功能
   - 支持PNG/SVG导出

3. **`frontend/src/types/plotly.d.ts`** - Plotly类型声明

4. **`frontend/src/utils/testPlotlyUtils.ts`** - 测试工具

#### 依赖安装

```bash
npm install plotly.js-dist-min
npm install --save-dev @types/plotly.js
```

### 3. 后端修改

**文件**: `backend/app/services/plot.py`
- 注释掉制流电路实验的图片生成器

**文件**: `backend/app/services/experiment.py`
- 为制流电路实验跳过图形生成逻辑

### 4. 前端组件修改

#### 修改的组件

1. **`frontend/src/experiments/CurrentControlCircuit.vue`**
   - 使用 `PlotlyChart` 组件替代图片显示
   - 调用 `generateCurrentControlCircuitPlot()` 生成图表

2. **`frontend/src/components/experiment-details/CurrentControlCircuitDetail.vue`**
   - 使用前端绘图显示历史数据
   - 移除图片URL相关逻辑

3. **`frontend/src/components/experiment-details/CurrentControlCircuitRecordTable.vue`**
   - 使用前端绘图替代图片显示
   - 移除图片下载和预览功能

4. **`frontend/src/services/experimentDataParser.ts`**
   - 更新JSON格式说明，标注图形在前端生成

### 5. 文档更新

**文件**: `README.md`
- 添加前端绘图系统说明
- 更新技术栈列表
- 更新JSON格式示例

## 技术优势

### 性能提升
- 减少服务器负载
- 图形生成更快
- 减少网络传输

### 用户体验
- 支持图表交互（缩放、平移、悬停）
- 实时数据更新
- 离线图表生成

### 开发维护
- 代码复用性更好
- 易于扩展新的图表类型
- 前后端职责分离更清晰

## 向后兼容性

- 现有包含 `plot_data` 的数据库记录仍可正常显示
- 前端组件能够处理有和没有 `plot_data` 的情况
- 管理员查看历史记录时，可以重新生成图表

## 测试验证

1. **类型检查**: `npm run type-check` 通过
2. **功能测试**: 可使用 `testPlotlyUtils()` 函数测试绘图功能
3. **组件测试**: 各个修改的组件都能正常编译

## 后续扩展

1. **其他实验类型**: 可以轻松添加新的绘图函数
2. **图表类型**: 支持更多Plotly图表类型
3. **交互功能**: 可以添加更多图表交互特性

## 注意事项

1. **首次加载**: Plotly.js采用动态导入，首次使用时需要加载
2. **浏览器兼容**: 需要支持ES6模块的现代浏览器
3. **内存管理**: 组件卸载时会自动清理Plotly实例

## 总结

本次迁移成功将制流电路实验的绘图功能从后端迁移到前端，实现了：

- ✅ 性能提升和用户体验改善
- ✅ 代码结构优化和职责分离
- ✅ 向后兼容性保持
- ✅ 易于扩展的架构设计

这为后续其他实验类型的前端绘图迁移奠定了良好的基础。
