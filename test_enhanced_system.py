#!/usr/bin/env python3
"""
增强版制流电路实验系统测试
测试新功能：
1. 制流电路实验的k=0.1和k=1双曲线绘制
2. 未分班分组学生表功能
3. 前端界面优化
"""

import requests
import json
import time

def test_backend_api():
    
    try:
        # 测试实验类型获取
        response = requests.get("http://localhost:8000/api/experiments/types", timeout=5)
        if response.status_code == 200:
            experiments = response.json()
            print(f"✅ 后端API正常 - 找到 {len(experiments)} 个实验类型")
            for exp in experiments:
                print(f"   - {exp['name']} ({exp['code']}) - {'可用' if exp['is_active'] else '不可用'}")
            return True
        else:
            print(f"❌ 后端API错误: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 后端连接失败: {e}")
        return False

def test_frontend_page():
    """测试前端页面"""
    print("🌐 测试前端页面...")
    
    try:
        response = requests.get("http://localhost:5174", timeout=5)
        if response.status_code == 200:
            print("✅ 前端页面正常加载")
            return True
        else:
            print(f"❌ 前端页面错误: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 前端连接失败: {e}")
        return False

def test_unassigned_student_feature():
    """测试未分班学生功能"""
    print("👤 测试未分班学生功能...")
    
    # 使用一个不存在的学号提交实验
    experiment_data = {
        "student_id": "2025999",  # 不存在的学号
        "name": "测试学生",
        "experiment_type_code": "current_control_circuit",
        "submission_data": {
            "k1Data": [
                {"ratio": 0.0, "current": 100.0},
                {"ratio": 0.5, "current": 75.0},
                {"ratio": 1.0, "current": 50.0}
            ],
            "k01Data": [
                {"ratio": 0.0, "current": 20.0},
                {"ratio": 0.5, "current": 15.0},
                {"ratio": 1.0, "current": 10.0}
            ]
        }
    }
    
    try:
        response = requests.post(
            "http://localhost:8000/api/experiments/submit",
            headers={"Content-Type": "application/json"},
            json=experiment_data,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 未分班学生功能测试成功!")
            print(f"   记录ID: {result.get('record_id')}")
            print(f"   消息: {result.get('message')}")
            
            # 检查是否包含未分班提示
            if "待分班学生列表" in result.get('message', ''):
                print("✅ 未分班学生提示正常显示")
                return True
            else:
                print("⚠️  未分班学生提示未显示")
                return False
        else:
            error_data = response.json()
            print(f"❌ 提交失败: {response.status_code}")
            print(f"   错误: {error_data.get('detail', '未知错误')}")
            return False
    except Exception as e:
        print(f"❌ 提交请求失败: {e}")
        return False

def test_enhanced_plotting():
    """测试增强的绘图功能"""
    print("📊 测试增强的绘图功能...")
    
    # 提交完整的实验数据
    experiment_data = {
        "student_id": "2021001",
        "name": "张三",
        "experiment_type_code": "current_control_circuit",
        "submission_data": {
            "k1Data": [
                {"ratio": 0.0, "current": 100.0},
                {"ratio": 0.1, "current": 95.0},
                {"ratio": 0.2, "current": 90.0},
                {"ratio": 0.3, "current": 85.0},
                {"ratio": 0.4, "current": 80.0},
                {"ratio": 0.5, "current": 75.0},
                {"ratio": 0.6, "current": 70.0},
                {"ratio": 0.7, "current": 65.0},
                {"ratio": 0.8, "current": 60.0},
                {"ratio": 0.9, "current": 55.0},
                {"ratio": 1.0, "current": 50.0}
            ],
            "k01Data": [
                {"ratio": 0.0, "current": 20.0},
                {"ratio": 0.1, "current": 19.0},
                {"ratio": 0.2, "current": 18.0},
                {"ratio": 0.3, "current": 17.0},
                {"ratio": 0.4, "current": 16.0},
                {"ratio": 0.5, "current": 15.0},
                {"ratio": 0.6, "current": 14.0},
                {"ratio": 0.7, "current": 13.0},
                {"ratio": 0.8, "current": 12.0},
                {"ratio": 0.9, "current": 11.0},
                {"ratio": 1.0, "current": 10.0}
            ]
        }
    }
    
    try:
        response = requests.post(
            "http://localhost:8000/api/experiments/submit",
            headers={"Content-Type": "application/json"},
            json=experiment_data,
            timeout=15
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 增强绘图功能测试成功!")
            print(f"   记录ID: {result.get('record_id')}")
            print(f"   是否通过: {result.get('is_passed')}")
            
            if result.get('plot_data'):
                plot_data_length = len(result.get('plot_data', ''))
                print(f"   📊 图表已生成 (大小: {plot_data_length} 字符)")
                print("   ✅ k=1和k=0.1双曲线绘制成功")
                return True
            else:
                print("   ❌ 图表生成失败")
                return False
        else:
            error_data = response.json()
            print(f"❌ 提交失败: {response.status_code}")
            print(f"   错误: {error_data.get('detail', '未知错误')}")
            return False
    except Exception as e:
        print(f"❌ 提交请求失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 70)
    print("增强版制流电路实验系统完整测试")
    print("=" * 70)
    
    # 测试各个组件
    backend_ok = test_backend_api()
    frontend_ok = test_frontend_page()
    unassigned_ok = test_unassigned_student_feature()
    plotting_ok = test_enhanced_plotting()
    
    print("\n" + "=" * 70)
    print("测试结果汇总:")
    print(f"后端API: {'✅ 正常' if backend_ok else '❌ 异常'}")
    print(f"前端页面: {'✅ 正常' if frontend_ok else '❌ 异常'}")
    print(f"未分班学生功能: {'✅ 正常' if unassigned_ok else '❌ 异常'}")
    print(f"增强绘图功能: {'✅ 正常' if plotting_ok else '❌ 异常'}")
    
    if all([backend_ok, frontend_ok, unassigned_ok, plotting_ok]):
        print("\n🎉 所有增强功能测试通过！系统完全正常！")
        print("\n📝 新功能说明:")
        print("1. ✅ k=0.1和k=1双曲线绘制 - 使用Plotly生成高质量图表")
        print("2. ✅ 未分班学生管理 - 自动添加未知学号到待分班列表")
        print("3. ✅ 前端界面优化 - 实验列表和详细信息展示")
        print("\n🔗 系统地址:")
        print("- 前端: http://localhost:5174")
        print("- 后端: http://localhost:8000")
        print("- 制流电路实验: http://localhost:5174/experiments/current-control-circuit")
    else:
        print("\n❌ 部分功能测试失败，请检查系统状态")
    
    print("=" * 70)

if __name__ == "__main__":
    main()
