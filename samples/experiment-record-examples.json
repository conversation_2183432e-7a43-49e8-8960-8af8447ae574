{"制流电路实验示例": {"实验数据": {"k=1电流数据": [{"接入比例": 0.1, "电流值(A)": 0.0012}, {"接入比例": 0.2, "电流值(A)": 0.0024}, {"接入比例": 0.3, "电流值(A)": 0.0036}, {"接入比例": 0.4, "电流值(A)": 0.0048}, {"接入比例": 0.5, "电流值(A)": 0.006}], "k=0.1电流数据": [{"接入比例": 0.1, "电流值(A)": 0.00012}, {"接入比例": 0.2, "电流值(A)": 0.00024}, {"接入比例": 0.3, "电流值(A)": 0.00036}, {"接入比例": 0.4, "电流值(A)": 0.00048}, {"接入比例": 0.5, "电流值(A)": 0.0006}], "实验环境": {"温度(°C)": 25.5, "湿度(%)": 60}, "备注": "实验过程正常，数据稳定"}, "实验图形": "base64图片数据", "实验分析": "根据测量数据分析，电流与接入比例呈线性关系，符合欧姆定律。k=1和k=0.1两种情况下的电流比值约为10:1，与理论值一致。实验结果良好。"}, "示波器实验示例": {"实验数据": {"频率(Hz)": 1000, "幅度(V)": 5.0, "波形类型": "正弦波", "测量数据": [{"时间(s)": 0.0, "电压(V)": 0.0}, {"时间(s)": 0.0001, "电压(V)": 2.9389}, {"时间(s)": 0.0002, "电压(V)": 4.7553}, {"时间(s)": 0.0003, "电压(V)": 4.7553}, {"时间(s)": 0.0004, "电压(V)": 2.9389}, {"时间(s)": 0.0005, "电压(V)": 0.0}, {"时间(s)": 0.0006, "电压(V)": -2.9389}, {"时间(s)": 0.0007, "电压(V)": -4.7553}, {"时间(s)": 0.0008, "电压(V)": -4.7553}, {"时间(s)": 0.0009, "电压(V)": -2.9389}, {"时间(s)": 0.001, "电压(V)": 0.0}], "备注": "波形稳定，无明显失真"}, "实验图形": "base64图片数据", "实验分析": "测量得到的正弦波形质量良好，频率为1000Hz，峰值电压约为5V，与设定参数一致。波形对称性好，无明显谐波失真。"}, "通用实验示例": {"实验数据": {"实验参数": {"参数1": "值1", "参数2": "值2", "参数3": "值3"}, "测量结果": [{"项目": "测量1", "数值": 123.45, "单位": "V"}, {"项目": "测量2", "数值": 67.89, "单位": "A"}, {"项目": "测量3", "数值": 98.76, "单位": "Ω"}], "备注": "这是一个通用格式的实验数据示例"}, "实验图形": "base64图片数据", "实验分析": "这是通用实验的AI分析结果示例。系统能够处理各种不同格式的实验数据。"}, "提交数据格式说明": {"制流电路实验提交格式": {"student_id": "学号", "student_name": "学生姓名", "k1Data": [{"ratio": 0.1, "current": 0.0012}], "k01Data": [{"ratio": 0.1, "current": 0.00012}], "temperature": 25.5, "humidity": 60, "notes": "备注信息"}, "示波器实验提交格式": {"student_id": "学号", "student_name": "学生姓名", "frequency": 1000, "amplitude": 5.0, "waveform_type": "正弦波", "measurements": [{"time": 0.0, "voltage": 0.0}], "notes": "备注信息"}, "通用实验提交格式": {"student_id": "学号", "student_name": "学生姓名", "任意字段名": "任意数据", "可以是对象": {"嵌套数据": "支持"}, "可以是数组": [1, 2, 3], "notes": "备注信息"}}, "系统特性说明": {"解析机制": "系统根据实验类型(experiment_code)自动选择对应的解析器", "支持的实验类型": ["current_control_circuit - 制流电路实验", "oscilloscope - 示波器实验", "其他类型 - 使用通用解析器"], "JSON格式": "所有实验记录都包含'实验数据'、'实验图形'、'实验分析'三个部分", "扩展性": "可以轻松添加新的实验类型和对应的解析器", "错误处理": "解析失败时会显示错误信息并使用通用格式展示"}}