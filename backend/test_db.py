#!/usr/bin/env python3
"""
测试数据库数据
"""

from sqlalchemy import create_engine, text
from app.utils.config import settings
from app.utils.auth import verify_password

def test_database():
    """测试数据库数据"""
    engine = create_engine(settings.database_url)
    
    with engine.connect() as conn:
        print("=== 管理员数据 ===")
        result = conn.execute(text("SELECT id, username, name, password_hash, is_active FROM admins"))
        for row in result:
            print(f"ID: {row[0]}, Username: {row[1]}, Name: {row[2]}, Active: {row[4]}")
            print(f"Password Hash: {row[3][:50]}...")
            
            # 测试密码验证
            if verify_password("admin123", row[3]):
                print("✓ 密码验证成功")
            else:
                print("✗ 密码验证失败")
            print()
        
        print("=== 学生数据 ===")
        result = conn.execute(text("SELECT id, student_id, name, password_hash, is_active FROM students LIMIT 3"))
        for row in result:
            print(f"ID: {row[0]}, Student ID: {row[1]}, Name: {row[2]}, Active: {row[4]}")
            print(f"Password Hash: {row[3][:50]}...")
            
            # 测试密码验证
            if verify_password("student123", row[3]):
                print("✓ 密码验证成功")
            else:
                print("✗ 密码验证失败")
            print()

if __name__ == "__main__":
    test_database()
