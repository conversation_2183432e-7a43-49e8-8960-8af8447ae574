"""
AI分析服务
"""

import httpx
import json
import numpy as np
from typing import Dict, Any, Tuple, Optional
import logging

from app.utils.config import settings

logger = logging.getLogger(__name__)


class AIAnalysisService:
    """AI分析服务类"""

    def __init__(self):
        self.ai_enabled = settings.AI_ANALYSIS_ENABLED
        self.ai_service_url = "http://localhost:20001/api/v1/chat/completions"
        self.ai_api_key = "sk-0e9eol_qOpYJPupuCvRcQY8NgSJwj_Ijo_coJLo1f9U"
        self.model_name = "Gemini 2.5 Flash"

        self.analyzers = {
            'current_control_circuit': self._analyze_current_control_circuit,
            'oscilloscope': self._analyze_oscilloscope,
        }
    
    def analyze_experiment(
        self,
        experiment_code: str,
        data: Dict[str, Any]
    ) -> Tuple[str, Optional[bool]]:
        """分析实验数据"""
        try:
            # 为前端联调提供稳定的模拟结果：直接返回“通过”
            if not self.ai_enabled:
                return "模拟分析：已接收数据，实验通过", True

            if experiment_code not in self.analyzers:
                # 未注册的实验类型也返回模拟通过，便于测试
                return "模拟分析：实验类型未注册，但已通过（测试模式）", True

            analyzer = self.analyzers[experiment_code]
            analysis_result, is_passed = analyzer(data)

            return analysis_result, is_passed

        except Exception as e:
            logger.error(f"AI分析失败: {e}")
            return f"AI分析出现错误: {str(e)}", None

    def analyze_experiment_data(
        self,
        experiment_code: str,
        data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """分析实验数据（返回字典格式）"""
        try:
            analysis_result, is_passed = self.analyze_experiment(experiment_code, data)
            return {
                "message": analysis_result,
                "is_passed": is_passed if is_passed is not None else True
            }
        except Exception as e:
            logger.error(f"AI分析失败: {e}")
            return {
                "message": f"AI分析出现错误: {str(e)}",
                "is_passed": False
            }
    
    def _analyze_current_control_circuit(self, data: Dict[str, Any]) -> Tuple[str, bool]:
        """分析制流电路实验数据"""
        try:
            k1_current = data.get('k1_current', [])
            k01_current = data.get('k01_current', [])

            if not k1_current or not k01_current:
                return "缺少必要的电流数据", False

            if len(k1_current) != 11 or len(k01_current) != 11:
                return "电流数据点数不正确，应为11个数据点", False

            # 使用AI进行分析
            return self._analyze_with_ai(k1_current, k01_current)

        except Exception as e:
            logger.error(f"分析制流电路数据失败: {e}")
            return f"分析过程中出现错误: {str(e)}", False
    
    def _analyze_with_ai(self, k1_current: list, k01_current: list) -> Tuple[str, bool]:
        """使用AI分析制流电路实验数据"""
        try:
            # 预处理数据
            processed_data = self._preprocess_data_zhiliu(k1_current, k01_current)

            # 构建分析提示
            experiment_description = f"""
# 制流电路实验数据分析
## 实验条件
1. 实验使用滑线变阻器和电阻箱构建制流电路
2. k值定义为电阻箱阻值(Rz)与滑线变阻器总阻值(R0)的比值: k = Rz/R0
3. 接入比例定义为滑动端到绕线一端的长度与滑线变阻器总长度的比值: l/l0
4. 实验分别测量了k=1和k=0.1两种情况下，接入比例从0.0到1.0变化时的电流值
5. 为了比较调节范围和调节精度，在k=1时，路端电压设置为5伏特，在k=0.1时，调节路端电压使接入比例为零时的电流与k=1时相等。

## 实验数据
接入比例从0.0到1.0，每次增加0.1:
k=1时的电流值(mA): {k1_current}
k=0.1时的电流值(mA): {k01_current}

## 预处理数据
理论计算k=1时的电流值(mA): {processed_data["theoretical_k1"]}
理论计算k=0.1时的电流值(mA): {processed_data["theoretical_k01"]}
k=1时的相对误差(%): {processed_data["relative_error_k1"]}
k=0.1时的相对误差(%): {processed_data["relative_error_k01"]}

## 需要分析的问题
1. 分析实验数据与理论计算的误差情况，指出可能的误差来源。
2. 判断存在以下误差：
    - a、如果接入比例为零时，电流不相等，提示需要将该电流调相等后重新测量。
    - b、该曲线起始下降速度较快，如果接入比例为0.1的点的相对误差较大为正值且较大，则可能是接入起始点选在了金属环的最外侧。
            应将金属环内侧（与电阻丝相接点）设置为接入比例为零点，以此计算接入比例。
    - c、如果某些点偏差较大，请具体指出这些点。
3. 给出实验正确与否的最终判断，是否需要重新测量。
"""

            # 调用AI API进行分析
            analysis_result = self._call_ai_api(experiment_description)

            # 生成总结
            summary_result = self._generate_summary(analysis_result, processed_data)

            # 判断是否通过
            is_passed = self._determine_pass_status(summary_result, processed_data)

            return summary_result, is_passed

        except Exception as e:
            logger.error(f"AI分析失败: {e}")
            return f"AI分析过程中发生错误: {str(e)}", False

    def _analyze_oscilloscope(self, data: Dict[str, Any]) -> Tuple[str, bool]:
        """分析示波器实验数据"""
        # 这里是示波器实验的分析逻辑
        # 暂时返回一个简单的分析结果
        return "示波器实验分析功能正在开发中", True
    
    def _preprocess_data_zhiliu(self, k1_current: list, k01_current: list) -> dict:
        """预处理制流电路实验数据 - 使用正确的理论计算公式"""
        ratios = np.arange(0, 1.1, 0.1)
        i0_k1 = k1_current[0]
        i0_k01 = k01_current[0]

        theoretical_k1 = [i0_k1 / (1 + 1 * ratio) if (1 + 1 * ratio) != 0 else float('inf') for ratio in ratios]
        theoretical_k01 = [i0_k01 / (1 + 10 * ratio) if (1 + 10 * ratio) != 0 else float('inf') for ratio in ratios]

        # Handle cases where theoretical is 0 to avoid division by zero
        relative_error_k1 = [
            ((actual - theo) / theo * 100) if theo != 0 else (float('inf') if actual != 0 else 0)
            for actual, theo in zip(k1_current, theoretical_k1)
        ]
        relative_error_k01 = [
            ((actual - theo) / theo * 100) if theo != 0 else (float('inf') if actual != 0 else 0)
            for actual, theo in zip(k01_current, theoretical_k01)
        ]

        return {
            "theoretical_k1": [round(x, 3) if x != float('inf') else 'inf' for x in theoretical_k1],
            "theoretical_k01": [round(x, 3) if x != float('inf') else 'inf' for x in theoretical_k01],
            "relative_error_k1": [round(x, 2) if x != float('inf') else 'inf' for x in relative_error_k1],
            "relative_error_k01": [round(x, 2) if x != float('inf') else 'inf' for x in relative_error_k01]
        }

    def _call_ai_api(self, prompt: str) -> str:
        """调用AI API进行分析"""
        try:
            with httpx.Client(timeout=60.0) as client:
                response = client.post(
                    self.ai_service_url,
                    headers={
                        "Content-Type": "application/json",
                        "Authorization": f"Bearer {self.ai_api_key}"
                    },
                    json={
                        "model": self.model_name,
                        "messages": [
                            {"role": "system", "content": "请根据提供的实验数据和条件，分析实验结果与理论计算的误差情况，并给出实验是否需要重新测量的判断。"},
                            {"role": "user", "content": prompt}
                        ],
                        "temperature": 0.2,
                        "max_tokens": 2500
                    }
                )

                if response.status_code == 200:
                    result = response.json()
                    return result["choices"][0]["message"]["content"]
                else:
                    logger.error(f"AI API调用失败: {response.status_code}, {response.text}")
                    return f"AI API调用失败: HTTP {response.status_code}"

        except Exception as e:
            logger.error(f"调用AI API时发生错误: {e}")
            return f"调用AI API时发生错误: {str(e)}"

    def _generate_summary(self, analysis_result: str, processed_data: dict) -> str:
        """生成分析总结"""
        try:
            summary_prompt = f"""
需要对制流电路实验的分析结果进行总结，并生成数据表格。

以下是详细的分析结果：
{analysis_result}

请执行以下任务：
1. 生成一个简洁的表格，包含三列：
   第一列：接入比例(从0.0到1.0，每次增加0.1)
   第二列：k=1时的相对误差(%): {processed_data["relative_error_k1"]}
   第三列：k=0.1时的相对误差(%): {processed_data["relative_error_k01"]}
   - 表格应该简洁明了，使用Markdown格式
2. 提炼分析中的内容，只保留提示和警告，对于正确的内容无需输出，不要输出分析过程。警告使用红色字体。
3. 给出最终结论：实验是否正确，是否需要重新测量,如果全部误差都在+-15%内，实验通过。
   - 最终结论应该明确指出实验是否需要重做，如果要求重新做，则<font color='red'>重新做</font>，如果通过，则<font color='green'>通过</font>。
"""

            return self._call_ai_api(summary_prompt)

        except Exception as e:
            logger.error(f"生成总结失败: {e}")
            return f"生成总结失败: {str(e)}"

    def _determine_pass_status(self, summary_result: str, processed_data: dict) -> bool:
        """根据分析结果判断是否通过"""
        try:
            # 检查总结中的结论
            if "<font color='green'>通过</font>" in summary_result:
                return True
            elif "<font color='red'>重新做</font>" in summary_result:
                return False

            # 如果没有明确结论，根据误差数据判断
            k1_errors = processed_data.get("relative_error_k1", [])
            k01_errors = processed_data.get("relative_error_k01", [])

            # 检查所有误差是否在±15%内
            all_errors = []
            for error in k1_errors + k01_errors:
                if error != 'inf' and isinstance(error, (int, float)):
                    all_errors.append(abs(error))

            if all_errors and max(all_errors) <= 15:
                return True
            else:
                return False

        except Exception as e:
            logger.error(f"判断通过状态失败: {e}")
            return False
