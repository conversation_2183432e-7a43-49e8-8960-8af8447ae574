"""
缓存服务 - 用于临时存储学生实验数据
"""
import json
import time
from typing import Dict, Any, Optional
from datetime import datetime, timedelta


class ExperimentCacheService:
    """实验数据缓存服务"""
    
    def __init__(self):
        # 使用内存缓存，生产环境可以替换为Redis
        self._cache: Dict[str, Dict[str, Any]] = {}
        self._expiry: Dict[str, float] = {}
        self._default_ttl = 3600 * 2  # 2小时过期
    
    def _generate_key(self, session_id: str, experiment_type: str) -> str:
        """生成缓存键"""
        return f"experiment:{session_id}:{experiment_type}"
    
    def _is_expired(self, key: str) -> bool:
        """检查缓存是否过期"""
        if key not in self._expiry:
            return True
        return time.time() > self._expiry[key]
    
    def _cleanup_expired(self):
        """清理过期的缓存"""
        current_time = time.time()
        expired_keys = [
            key for key, expiry_time in self._expiry.items()
            if current_time > expiry_time
        ]
        for key in expired_keys:
            self._cache.pop(key, None)
            self._expiry.pop(key, None)
    
    def save_experiment_data(
        self, 
        session_id: str, 
        experiment_type: str, 
        data: Dict[str, Any],
        ttl: Optional[int] = None
    ) -> bool:
        """
        保存实验数据到缓存
        
        Args:
            session_id: 会话ID（可以是cookie中的值）
            experiment_type: 实验类型
            data: 要缓存的数据
            ttl: 过期时间（秒），默认2小时
        
        Returns:
            bool: 是否保存成功
        """
        try:
            self._cleanup_expired()
            
            key = self._generate_key(session_id, experiment_type)
            cache_data = {
                'data': data,
                'timestamp': datetime.now().isoformat(),
                'session_id': session_id,
                'experiment_type': experiment_type
            }
            
            self._cache[key] = cache_data
            self._expiry[key] = time.time() + (ttl or self._default_ttl)
            
            return True
        except Exception as e:
            print(f"缓存保存失败: {e}")
            return False
    
    def get_experiment_data(
        self, 
        session_id: str, 
        experiment_type: str
    ) -> Optional[Dict[str, Any]]:
        """
        获取缓存的实验数据
        
        Args:
            session_id: 会话ID
            experiment_type: 实验类型
        
        Returns:
            Dict[str, Any] | None: 缓存的数据，如果不存在或过期则返回None
        """
        try:
            self._cleanup_expired()
            
            key = self._generate_key(session_id, experiment_type)
            
            if key not in self._cache or self._is_expired(key):
                return None
            
            return self._cache[key]['data']
        except Exception as e:
            print(f"缓存获取失败: {e}")
            return None
    
    def delete_experiment_data(
        self, 
        session_id: str, 
        experiment_type: str
    ) -> bool:
        """
        删除缓存的实验数据
        
        Args:
            session_id: 会话ID
            experiment_type: 实验类型
        
        Returns:
            bool: 是否删除成功
        """
        try:
            key = self._generate_key(session_id, experiment_type)
            self._cache.pop(key, None)
            self._expiry.pop(key, None)
            return True
        except Exception as e:
            print(f"缓存删除失败: {e}")
            return False
    
    def get_cache_info(self, session_id: str) -> Dict[str, Any]:
        """
        获取指定会话的缓存信息
        
        Args:
            session_id: 会话ID
        
        Returns:
            Dict[str, Any]: 缓存信息
        """
        self._cleanup_expired()
        
        session_caches = []
        for key, data in self._cache.items():
            if data.get('session_id') == session_id:
                session_caches.append({
                    'experiment_type': data.get('experiment_type'),
                    'timestamp': data.get('timestamp'),
                    'has_data': bool(data.get('data'))
                })
        
        return {
            'session_id': session_id,
            'cached_experiments': session_caches,
            'total_count': len(session_caches)
        }


# 全局缓存实例
experiment_cache = ExperimentCacheService()
