"""
认证服务
"""

from sqlalchemy.orm import Session
from typing import Optional

from app.models.user import Student, Admin
from app.utils.auth import verify_password


class AuthService:
    """认证服务类"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def authenticate_student(self, student_id: str, password: str) -> Optional[Student]:
        """学生认证"""
        student = self.db.query(Student).filter(
            Student.student_id == student_id,
            Student.is_active == True
        ).first()
        
        if student and student.password_hash and verify_password(password, student.password_hash):
            return student
        
        return None
    
    def authenticate_admin(self, username: str, password: str) -> Optional[Admin]:
        """管理员认证"""
        admin = self.db.query(Admin).filter(
            Admin.username == username,
            Admin.is_active == True
        ).first()
        
        if admin and verify_password(password, admin.password_hash):
            return admin
        
        return None
