"""
学生服务
"""

from sqlalchemy.orm import Session
from typing import List, Optional

from app.models.user import Student, Class, Group


class StudentService:
    """学生服务类"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_student_by_id(self, student_id: int) -> Optional[Student]:
        """根据ID获取学生信息"""
        return self.db.query(Student).filter(Student.id == student_id).first()
    
    def get_student_by_student_id(self, student_id: str) -> Optional[Student]:
        """根据学号获取学生信息"""
        return self.db.query(Student).filter(Student.student_id == student_id).first()
    
    def get_classmates(self, class_id: int) -> List[Student]:
        """获取同班同学"""
        return self.db.query(Student).filter(
            Student.class_id == class_id,
            Student.is_active == True
        ).all()
    
    def get_group_members(self, group_id: int) -> List[Student]:
        """获取同组成员"""
        return self.db.query(Student).filter(
            Student.group_id == group_id,
            Student.is_active == True
        ).all()
