"""
图形绘制服务
"""

import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import base64
import io
import json
from typing import Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)


class PlotService:
    """图形绘制服务类"""
    
    def __init__(self):
        self.plot_generators = {
            # 'current_control_circuit': self._generate_current_control_plot,  # 已迁移到前端
            'oscilloscope': self._generate_oscilloscope_plot,
        }
    
    def generate_plot(self, experiment_code: str, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """根据实验类型生成图形，返回包含多种格式的字典"""
        try:
            if experiment_code not in self.plot_generators:
                logger.warning(f"不支持的实验类型: {experiment_code}")
                return None

            generator = self.plot_generators[experiment_code]
            plot_result = generator(data)

            return plot_result

        except Exception as e:
            logger.error(f"生成图形失败: {e}")
            raise
    
    def _generate_current_control_plot(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """生成制流电路实验图形"""
        try:
            # 从数据中提取电流值与绘图选项
            k1_data = data.get('k1Data', [])
            k01_data = data.get('k01Data', [])
            smooth = bool(data.get('smooth', False))
            lang = data.get('lang', 'zh')  # 'zh' 或 'en'

            if not k1_data or not k01_data:
                raise ValueError("缺少必要的电流数据")

            # 提取数据
            k1_ratios = [item.get('ratio', 0) for item in k1_data]
            k1_currents = [item.get('current', 0) for item in k1_data]
            k01_ratios = [item.get('ratio', 0) for item in k01_data]
            k01_currents = [item.get('current', 0) for item in k01_data]

            # 可选：对曲线进行插值平滑（线性分段插值，避免 SciPy 依赖）
            def densify(xs, ys, num=201):
                if not xs or not ys or len(xs) != len(ys):
                    return xs, ys
                # 确保按 x 升序
                pairs = sorted(zip(xs, ys), key=lambda p: p[0])
                xs_sorted = [p[0] for p in pairs]
                ys_sorted = [p[1] for p in pairs]
                x_min, x_max = xs_sorted[0], xs_sorted[-1]
                if x_max == x_min:
                    return xs_sorted, ys_sorted
                step = (x_max - x_min) / (num - 1)
                dense_x = [x_min + i * step for i in range(num)]
                dense_y = []
                j = 0
                for x in dense_x:
                    while j < len(xs_sorted) - 2 and x > xs_sorted[j+1]:
                        j += 1
                    x0, y0 = xs_sorted[j], ys_sorted[j]
                    x1, y1 = xs_sorted[j+1], ys_sorted[j+1]
                    t = 0 if x1 == x0 else (x - x0) / (x1 - x0)
                    y = y0 + t * (y1 - y0)
                    dense_y.append(y)
                return dense_x, dense_y

            if smooth and len(k1_ratios) >= 2 and len(k01_ratios) >= 2:
                k1_line_x, k1_line_y = densify(k1_ratios, k1_currents)
                k01_line_x, k01_line_y = densify(k01_ratios, k01_currents)
            else:
                k1_line_x, k1_line_y = k1_ratios, k1_currents
                k01_line_x, k01_line_y = k01_ratios, k01_currents

            # 创建图形
            fig = go.Figure()

            # 添加k=1的曲线（线：可插值；点：原始数据）
            fig.add_trace(go.Scatter(
                x=k1_line_x,
                y=k1_line_y,
                mode='lines',
                name='k=1',
                line=dict(color='#1f77b4', width=3)
            ))
            fig.add_trace(go.Scatter(
                x=k1_ratios,
                y=k1_currents,
                mode='markers',
                name='k=1 原始点',
                marker=dict(size=7, symbol='circle', color='#1f77b4')
            ))

            # 添加k=0.1的曲线（线：可插值；点：原始数据）
            fig.add_trace(go.Scatter(
                x=k01_line_x,
                y=k01_line_y,
                mode='lines',
                name='k=0.1',
                line=dict(color='#ff7f0e', width=3)
            ))
            fig.add_trace(go.Scatter(
                x=k01_ratios,
                y=k01_currents,
                mode='markers',
                name='k=0.1 原始点',
                marker=dict(size=7, symbol='diamond', color='#ff7f0e')
            ))

            # 轴标题语言
            if lang == 'en':
                title_text = 'Current control circuit - I vs ratio'
                x_title = 'Access ratio'
                y_title = 'Current (mA)'
            else:
                title_text = '制流电路实验 - 电流与接入比例关系'
                x_title = '接入比例'
                y_title = '电流 (mA)'

            # 设置图形布局（提供支持中文的字体栈，若服务器无中文字体将回退英文字体）
            font_stack = '"Noto Sans CJK SC", "Microsoft YaHei", "SimHei", "Arial Unicode MS", "DejaVu Sans", Arial, sans-serif'
            fig.update_layout(
                title={
                    'text': title_text,
                    'x': 0.5,
                    'xanchor': 'center',
                    'font': {'size': 18, 'family': font_stack}
                },
                xaxis_title=x_title,
                yaxis_title=y_title,
                legend=dict(
                    x=0.02,
                    y=0.98,
                    bgcolor='rgba(255,255,255,0.8)',
                    bordercolor='rgba(0,0,0,0.2)',
                    borderwidth=1
                ),
                width=800,
                height=600,
                template='plotly_white',
                font=dict(family=font_stack, size=12),
                plot_bgcolor='rgba(0,0,0,0)',
                paper_bgcolor='rgba(0,0,0,0)'
            )

            # 添加网格
            fig.update_xaxes(
                showgrid=True,
                gridwidth=1,
                gridcolor='rgba(128,128,128,0.3)',
                title_font=dict(size=14)
            )
            fig.update_yaxes(
                showgrid=True,
                gridwidth=1,
                gridcolor='rgba(128,128,128,0.3)',
                title_font=dict(size=14)
            )

            # 生成多种格式的输出
            # 1. PNG图片的base64编码
            img_bytes = fig.to_image(format="png", width=800, height=600)
            img_base64 = base64.b64encode(img_bytes).decode('utf-8')

            # 2. Plotly JSON格式（用于前端交互）
            plotly_json = fig.to_json()

            # 3. 数据摘要
            data_summary = {
                'k1_points': len(k1_data),
                'k01_points': len(k01_data),
                'k1_current_range': [min(k1_currents), max(k1_currents)] if k1_currents else [0, 0],
                'k01_current_range': [min(k01_currents), max(k01_currents)] if k01_currents else [0, 0]
            }

            return {
                'image_base64': img_base64,
                'plotly_json': plotly_json,
                'data_summary': data_summary,
                'chart_type': 'current_control_circuit',
                'title': '制流电路实验 - 电流与接入比例关系'
            }
            
        except Exception as e:
            logger.error(f"生成制流电路图形失败: {e}")
            raise
    
    def _generate_oscilloscope_plot(self, data: Dict[str, Any]) -> str:
        """生成示波器实验图形"""
        try:
            # 这里是示波器实验的图形生成逻辑
            # 暂时返回一个简单的示例图形
            
            import numpy as np
            
            # 生成示例波形数据
            t = np.linspace(0, 2*np.pi, 100)
            y1 = np.sin(t)
            y2 = np.sin(2*t)
            
            fig = go.Figure()
            
            fig.add_trace(go.Scatter(
                x=t,
                y=y1,
                mode='lines',
                name='信号1',
                line=dict(color='blue', width=2)
            ))
            
            fig.add_trace(go.Scatter(
                x=t,
                y=y2,
                mode='lines',
                name='信号2',
                line=dict(color='red', width=2)
            ))
            
            fig.update_layout(
                title='示波器实验 - 波形显示',
                xaxis_title='时间 (s)',
                yaxis_title='电压 (V)',
                width=800,
                height=600,
                template='plotly_white'
            )
            
            # 转换为base64字符串
            img_bytes = fig.to_image(format="png", width=800, height=600)
            img_base64 = base64.b64encode(img_bytes).decode('utf-8')
            
            return img_base64
            
        except Exception as e:
            logger.error(f"生成示波器图形失败: {e}")
            raise
