"""
管理员服务
"""

from sqlalchemy.orm import Session
from sqlalchemy import func, desc, and_, or_, case
from typing import List, Optional, Tuple, Dict, Any
import json
import csv
import io
from datetime import datetime, timedelta

from app.models.user import Student, Admin, Class, Group
from app.models.experiment import ExperimentRecord, ExperimentType, ExperimentStatus


class AdminService:
    """管理员服务类"""

    def __init__(self, db: Session):
        self.db = db

    def get_students(self, class_id: Optional[int] = None) -> List[Student]:
        """获取学生列表"""
        query = self.db.query(Student)

        if class_id:
            query = query.filter(Student.class_id == class_id)

        return query.all()

    def get_classes(self) -> List[Class]:
        """获取班级列表"""
        return self.db.query(Class).all()

    def create_class(self, name: str, code: str, department: Optional[str] = None, grade: Optional[int] = None) -> Class:
        """创建新班级"""
        # 检查班级代码是否已存在
        existing_class = self.db.query(Class).filter(Class.code == code).first()
        if existing_class:
            raise ValueError(f"班级代码 {code} 已存在")

        new_class = Class(
            name=name,
            code=code,
            department=department,
            grade=grade
        )

        self.db.add(new_class)
        self.db.commit()
        self.db.refresh(new_class)

        return new_class

    def update_class(self, class_id: int, name: Optional[str] = None, code: Optional[str] = None,
                    department: Optional[str] = None, grade: Optional[int] = None) -> Class:
        """更新班级信息"""
        class_obj = self.db.query(Class).filter(Class.id == class_id).first()
        if not class_obj:
            raise ValueError(f"班级 ID {class_id} 不存在")

        # 如果更新代码，检查是否与其他班级冲突
        if code and code != class_obj.code:
            existing_class = self.db.query(Class).filter(Class.code == code, Class.id != class_id).first()
            if existing_class:
                raise ValueError(f"班级代码 {code} 已存在")

        if name is not None:
            class_obj.name = name
        if code is not None:
            class_obj.code = code
        if department is not None:
            class_obj.department = department
        if grade is not None:
            class_obj.grade = grade

        self.db.commit()
        self.db.refresh(class_obj)

        return class_obj

    def delete_class(self, class_id: int) -> None:
        """删除班级"""
        class_obj = self.db.query(Class).filter(Class.id == class_id).first()
        if not class_obj:
            raise ValueError(f"班级 ID {class_id} 不存在")

        # 检查是否有学生在该班级中
        student_count = self.db.query(Student).filter(Student.class_id == class_id).count()
        if student_count > 0:
            raise ValueError(f"班级中还有 {student_count} 名学生，无法删除")

        self.db.delete(class_obj)
        self.db.commit()

    def get_groups(self, class_id: Optional[int] = None) -> List[Group]:
        """获取分组列表"""
        query = self.db.query(Group)

        if class_id:
            query = query.filter(Group.class_id == class_id)

        return query.all()

    def create_student(self, student_id: str, name: str, class_id: int,
                      group_id: Optional[int] = None, email: Optional[str] = None) -> Student:
        """创建新学生"""
        # 检查学号是否已存在
        existing_student = self.db.query(Student).filter(Student.student_id == student_id).first()
        if existing_student:
            raise ValueError(f"学号 {student_id} 已存在")

        # 检查班级是否存在
        class_obj = self.db.query(Class).filter(Class.id == class_id).first()
        if not class_obj:
            raise ValueError(f"班级 ID {class_id} 不存在")

        # 检查分组是否存在（如果提供了分组ID）
        if group_id:
            group_obj = self.db.query(Group).filter(Group.id == group_id).first()
            if not group_obj:
                raise ValueError(f"分组 ID {group_id} 不存在")
            if group_obj.class_id != class_id:
                raise ValueError(f"分组 {group_id} 不属于班级 {class_id}")

        new_student = Student(
            student_id=student_id,
            name=name,
            class_id=class_id,
            group_id=group_id,
            email=email,
            is_active=True
        )

        self.db.add(new_student)
        self.db.commit()
        self.db.refresh(new_student)

        return new_student

    def update_student(self, student_id: int, student_no: Optional[str] = None, name: Optional[str] = None,
                      class_id: Optional[int] = None, group_id: Optional[int] = None,
                      email: Optional[str] = None, is_active: Optional[bool] = None) -> Student:
        """更新学生信息"""
        student = self.db.query(Student).filter(Student.id == student_id).first()
        if not student:
            raise ValueError(f"学生 ID {student_id} 不存在")

        # 如果更新学号，检查是否与其他学生冲突
        if student_no and student_no != student.student_id:
            existing_student = self.db.query(Student).filter(Student.student_id == student_no, Student.id != student_id).first()
            if existing_student:
                raise ValueError(f"学号 {student_no} 已存在")

        # 如果更新班级，检查班级是否存在
        if class_id and class_id != student.class_id:
            class_obj = self.db.query(Class).filter(Class.id == class_id).first()
            if not class_obj:
                raise ValueError(f"班级 ID {class_id} 不存在")

        # 如果更新分组，检查分组是否存在且属于正确的班级
        if group_id:
            group_obj = self.db.query(Group).filter(Group.id == group_id).first()
            if not group_obj:
                raise ValueError(f"分组 ID {group_id} 不存在")
            target_class_id = class_id if class_id else student.class_id
            if group_obj.class_id != target_class_id:
                raise ValueError(f"分组 {group_id} 不属于目标班级")

        if student_no is not None:
            student.student_id = student_no
        if name is not None:
            student.name = name
        if class_id is not None:
            student.class_id = class_id
        if group_id is not None:
            student.group_id = group_id
        if email is not None:
            student.email = email
        if is_active is not None:
            student.is_active = is_active

        self.db.commit()
        self.db.refresh(student)

        return student

    def delete_student(self, student_id: int) -> None:
        """删除学生"""
        student = self.db.query(Student).filter(Student.id == student_id).first()
        if not student:
            raise ValueError(f"学生 ID {student_id} 不存在")

        # 检查是否有实验记录
        record_count = self.db.query(ExperimentRecord).filter(ExperimentRecord.student_id == student_id).count()
        if record_count > 0:
            raise ValueError(f"学生有 {record_count} 条实验记录，无法删除")

        self.db.delete(student)
        self.db.commit()

    def import_students_from_csv(self, csv_content: str) -> Dict[str, Any]:
        """从CSV导入学生"""
        success_count = 0
        error_count = 0
        errors = []

        try:
            csv_reader = csv.DictReader(io.StringIO(csv_content))

            for row_num, row in enumerate(csv_reader, start=2):  # 从第2行开始（第1行是标题）
                try:
                    # 必需字段
                    student_id = row.get('student_id', '').strip()
                    name = row.get('name', '').strip()
                    class_code = row.get('class_code', '').strip()

                    if not student_id or not name or not class_code:
                        errors.append(f"第{row_num}行：学号、姓名、班级代码不能为空")
                        error_count += 1
                        continue

                    # 查找班级
                    class_obj = self.db.query(Class).filter(Class.code == class_code).first()
                    if not class_obj:
                        errors.append(f"第{row_num}行：班级代码 {class_code} 不存在")
                        error_count += 1
                        continue

                    # 可选字段
                    email = row.get('email', '').strip() or None
                    group_name = row.get('group_name', '').strip()

                    group_id = None
                    if group_name:
                        group_obj = self.db.query(Group).filter(
                            Group.name == group_name,
                            Group.class_id == class_obj.id
                        ).first()
                        if group_obj:
                            group_id = group_obj.id
                        else:
                            errors.append(f"第{row_num}行：分组 {group_name} 在班级 {class_code} 中不存在")
                            error_count += 1
                            continue

                    # 检查学号是否已存在
                    existing_student = self.db.query(Student).filter(Student.student_id == student_id).first()
                    if existing_student:
                        errors.append(f"第{row_num}行：学号 {student_id} 已存在")
                        error_count += 1
                        continue

                    # 创建学生
                    new_student = Student(
                        student_id=student_id,
                        name=name,
                        class_id=class_obj.id,
                        group_id=group_id,
                        email=email,
                        is_active=True
                    )

                    self.db.add(new_student)
                    success_count += 1

                except Exception as e:
                    errors.append(f"第{row_num}行：{str(e)}")
                    error_count += 1

            if success_count > 0:
                self.db.commit()

        except Exception as e:
            errors.append(f"CSV解析错误：{str(e)}")
            error_count += 1

        return {
            "success_count": success_count,
            "error_count": error_count,
            "errors": errors
        }

    def get_experiment_records(
        self,
        student_id: Optional[int] = None,
        experiment_type_id: Optional[int] = None,
        status: Optional[str] = None
    ) -> List[ExperimentRecord]:
        """获取实验记录（简单过滤，不带分页）"""
        query = self.db.query(ExperimentRecord)

        if student_id:
            query = query.filter(ExperimentRecord.student_id == student_id)

        if experiment_type_id:
            query = query.filter(ExperimentRecord.experiment_type_id == experiment_type_id)

        if status:
            query = query.filter(ExperimentRecord.status == status)

        return query.all()

    def query_experiment_records(
        self,
        page: int = 1,
        page_size: int = 20,
        experiment_code: Optional[str] = None,
        student_no: Optional[str] = None,
        student_name: Optional[str] = None,
        date_from: Optional[datetime] = None,
        date_to: Optional[datetime] = None,
        status: Optional[str] = None,
    ) -> Tuple[List[Dict[str, Any]], int]:
        """
        获取实验记录（高级过滤+分页），返回（记录列表，总数）
        记录包含便于展示的字段：学生姓名/学号（含未分班回退）、实验名称/代码、提交时间、得分、状态
        """
        q = (
            self.db.query(ExperimentRecord, Student, ExperimentType)
            .outerjoin(Student, ExperimentRecord.student_id == Student.id)
            .outerjoin(ExperimentType, ExperimentRecord.experiment_type_id == ExperimentType.id)
        )

        if experiment_code:
            q = q.filter(ExperimentType.code == experiment_code)
        if student_no:
            q = q.filter(Student.student_id.like(f"%{student_no}%"))
        if student_name:
            q = q.filter(Student.name.like(f"%{student_name}%"))
        if status:
            q = q.filter(ExperimentRecord.status == status)
        if date_from:
            q = q.filter(ExperimentRecord.submitted_at >= date_from)
        if date_to:
            q = q.filter(ExperimentRecord.submitted_at <= date_to)

        total = q.count()
        q = q.order_by(ExperimentRecord.submitted_at.desc()).offset((page - 1) * page_size).limit(page_size)

        items: List[Dict[str, Any]] = []
        for rec, stu, et in q.all():
            # 回退获取：若无学生关联（未分班），尝试从 submission_data 中取
            sub_student_id = None
            sub_student_name = None
            try:
                data = rec.submission_data
                if isinstance(data, str):
                    data = json.loads(data)
                if isinstance(data, dict):
                    sub_student_id = data.get("student_id")
                    sub_student_name = data.get("student_name")
            except Exception:
                pass

            items.append({
                "id": rec.id,
                "student_name": stu.name if stu else sub_student_name,
                "student_no": stu.student_id if stu else sub_student_id,
                "experiment_name": et.name if et else None,
                "experiment_code": et.code if et else None,
                "submitted_at": rec.submitted_at.isoformat() if rec.submitted_at else None,
                "score": float(rec.score) if rec.score is not None else None,
                "status": rec.status,
                "is_passed": rec.is_passed,
            })
        return items, total

    def get_experiment_statistics(self) -> List[dict]:
        """获取实验统计信息"""
        # 查询每个实验类型的统计信息
        stats = self.db.query(
            ExperimentType.name,
            func.count(ExperimentRecord.id).label('total_submissions'),
            func.sum(func.case([(ExperimentRecord.is_passed == True, 1)], else_=0)).label('passed_count'),
            func.sum(func.case([(ExperimentRecord.is_passed == False, 1)], else_=0)).label('failed_count'),
            func.sum(func.case([(ExperimentRecord.is_passed == None, 1)], else_=0)).label('pending_count')
        ).outerjoin(
            ExperimentRecord, ExperimentType.id == ExperimentRecord.experiment_type_id
        ).group_by(ExperimentType.id, ExperimentType.name).all()

        result = []
        for stat in stats:
            total = stat.total_submissions or 0
            passed = stat.passed_count or 0
            failed = stat.failed_count or 0
            pending = stat.pending_count or 0

            pass_rate = (passed / total * 100) if total > 0 else 0

            result.append({
                "experiment_name": stat.name,
                "total_submissions": total,
                "passed_count": passed,
                "failed_count": failed,
                "pending_count": pending,
                "pass_rate": round(pass_rate, 2)
            })

        return result

    def get_recent_activities(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取最近活动（按提交时间倒序）"""
        q = (
            self.db.query(ExperimentRecord, Student, ExperimentType)
            .outerjoin(Student, ExperimentRecord.student_id == Student.id)
            .outerjoin(ExperimentType, ExperimentRecord.experiment_type_id == ExperimentType.id)
            .order_by(ExperimentRecord.submitted_at.desc())
            .limit(limit)
        )
        items: List[Dict[str, Any]] = []
        for rec, stu, et in q.all():
            # 兼容未分班提交的数据
            sub_student_id = None
            sub_student_name = None
            try:
                data = rec.submission_data
                if isinstance(data, str):
                    data = json.loads(data)
                if isinstance(data, dict):
                    sub_student_id = data.get("student_id")
                    sub_student_name = data.get("student_name")
            except Exception:
                pass

            items.append({
                "id": rec.id,
                "submitted_at": rec.submitted_at.isoformat() if rec.submitted_at else None,
                "student_no": stu.student_id if stu else sub_student_id,
                "student_name": stu.name if stu else sub_student_name,
                "experiment_name": et.name if et else None,
                "experiment_code": et.code if et else None,
                "status": rec.status,
                "is_passed": rec.is_passed,
                "score": float(rec.score) if rec.score is not None else None,
            })
        return items

    def get_overview_stats(self) -> Dict[str, Any]:
        """获取概览统计数据"""
        try:
            total_students = self.db.query(func.count(Student.id)).scalar() or 0
            total_classes = self.db.query(func.count(Class.id)).scalar() or 0
            total_experiments = self.db.query(func.count(ExperimentType.id)).scalar() or 0
            total_submissions = self.db.query(func.count(ExperimentRecord.id)).scalar() or 0

            # 通过/未通过/待审核数量
            passed = self.db.query(func.count(ExperimentRecord.id)).filter(ExperimentRecord.is_passed == True).scalar() or 0
            failed = self.db.query(func.count(ExperimentRecord.id)).filter(ExperimentRecord.is_passed == False).scalar() or 0
            pending = self.db.query(func.count(ExperimentRecord.id)).filter(ExperimentRecord.is_passed == None).scalar() or 0

            pass_rate = round((float(passed) / float(total_submissions) * 100.0), 2) if total_submissions > 0 else 0.0

            try:
                recent = self.get_recent_activities(limit=10)
            except Exception as e:
                print(f"获取最近活动失败: {e}")
                recent = []

            return {
                "total_students": int(total_students),
                "total_classes": int(total_classes),
                "total_experiments": int(total_experiments),
                "total_submissions": int(total_submissions),
                "passed_count": int(passed or 0),
                "failed_count": int(failed or 0),
                "pending_count": int(pending or 0),
                "pass_rate": pass_rate,
                "recent_activities": recent,
            }
        except Exception as e:
            print(f"获取概览统计数据异常: {e}")
            # 返回基本数据
            return {
                "total_students": self.db.query(func.count(Student.id)).scalar() or 0,
                "total_classes": self.db.query(func.count(Class.id)).scalar() or 0,
                "total_experiments": 0,
                "total_submissions": 0,
                "passed_count": 0,
                "failed_count": 0,
                "pending_count": 0,
                "pass_rate": 0.0,
                "recent_activities": [],
            }


    def get_learning_progress_stats(self, class_id: Optional[int] = None, group_id: Optional[int] = None) -> Dict[str, Any]:
        """获取学习进度统计"""

        # 基础查询
        base_query = self.db.query(ExperimentRecord).join(Student, ExperimentRecord.student_id == Student.id)

        if class_id:
            base_query = base_query.filter(Student.class_id == class_id)
        if group_id:
            base_query = base_query.filter(Student.group_id == group_id)

        # 按班级统计
        class_stats = []
        classes_query = self.db.query(Class)
        if class_id:
            classes_query = classes_query.filter(Class.id == class_id)

        for cls in classes_query.all():
            class_records = self.db.query(ExperimentRecord).join(Student).filter(Student.class_id == cls.id)
            total_submissions = class_records.count()
            passed_submissions = class_records.filter(ExperimentRecord.is_passed == True).count()
            avg_score = class_records.filter(ExperimentRecord.score.isnot(None)).with_entities(func.avg(ExperimentRecord.score)).scalar()

            class_stats.append({
                "class_id": cls.id,
                "class_name": cls.name,
                "class_code": cls.code,
                "total_students": len(cls.students),
                "total_submissions": total_submissions,
                "passed_submissions": passed_submissions,
                "pass_rate": round((passed_submissions / total_submissions * 100), 2) if total_submissions > 0 else 0,
                "avg_score": round(float(avg_score), 2) if avg_score else 0
            })

        # 按分组统计
        group_stats = []
        groups_query = self.db.query(Group).join(Class)
        if class_id:
            groups_query = groups_query.filter(Class.id == class_id)
        if group_id:
            groups_query = groups_query.filter(Group.id == group_id)

        for group in groups_query.all():
            group_records = self.db.query(ExperimentRecord).join(Student).filter(Student.group_id == group.id)
            total_submissions = group_records.count()
            passed_submissions = group_records.filter(ExperimentRecord.is_passed == True).count()
            avg_score = group_records.filter(ExperimentRecord.score.isnot(None)).with_entities(func.avg(ExperimentRecord.score)).scalar()

            group_stats.append({
                "group_id": group.id,
                "group_name": group.name,
                "class_name": group.class_info.name,
                "total_students": len(group.students),
                "total_submissions": total_submissions,
                "passed_submissions": passed_submissions,
                "pass_rate": round((passed_submissions / total_submissions * 100), 2) if total_submissions > 0 else 0,
                "avg_score": round(float(avg_score), 2) if avg_score else 0
            })

        # 按学生统计（前20名活跃学生）
        student_stats = []
        students_query = self.db.query(Student)
        if class_id:
            students_query = students_query.filter(Student.class_id == class_id)
        if group_id:
            students_query = students_query.filter(Student.group_id == group_id)

        for student in students_query.limit(20).all():
            student_records = self.db.query(ExperimentRecord).filter(ExperimentRecord.student_id == student.id)
            total_submissions = student_records.count()
            passed_submissions = student_records.filter(ExperimentRecord.is_passed == True).count()
            avg_score = student_records.filter(ExperimentRecord.score.isnot(None)).with_entities(func.avg(ExperimentRecord.score)).scalar()
            last_submission = student_records.order_by(desc(ExperimentRecord.submitted_at)).first()

            student_stats.append({
                "student_id": student.id,
                "student_no": student.student_id,
                "student_name": student.name,
                "class_name": student.class_info.name if student.class_info else None,
                "group_name": student.group_info.name if student.group_info else None,
                "total_submissions": total_submissions,
                "passed_submissions": passed_submissions,
                "pass_rate": round((passed_submissions / total_submissions * 100), 2) if total_submissions > 0 else 0,
                "avg_score": round(float(avg_score), 2) if avg_score else 0,
                "last_submission_at": last_submission.submitted_at.isoformat() if last_submission and last_submission.submitted_at else None
            })

        # 按实验类型统计覆盖率
        experiment_stats = []
        for exp_type in self.db.query(ExperimentType).filter(ExperimentType.is_active == True).all():
            exp_records = base_query.filter(ExperimentRecord.experiment_type_id == exp_type.id)
            total_submissions = exp_records.count()
            passed_submissions = exp_records.filter(ExperimentRecord.is_passed == True).count()

            # 计算覆盖率（有多少学生提交过这个实验）
            unique_students = exp_records.with_entities(ExperimentRecord.student_id).distinct().count()
            total_students_in_scope = students_query.count() if class_id or group_id else self.db.query(Student).count()
            coverage_rate = round((unique_students / total_students_in_scope * 100), 2) if total_students_in_scope > 0 else 0

            experiment_stats.append({
                "experiment_id": exp_type.id,
                "experiment_name": exp_type.name,
                "experiment_code": exp_type.code,
                "total_submissions": total_submissions,
                "passed_submissions": passed_submissions,
                "pass_rate": round((passed_submissions / total_submissions * 100), 2) if total_submissions > 0 else 0,
                "unique_students": unique_students,
                "coverage_rate": coverage_rate
            })

        return {
            "class_stats": class_stats,
            "group_stats": group_stats,
            "student_stats": student_stats,
            "experiment_stats": experiment_stats
        }

    def get_submission_trends(self, days: int = 30, class_id: Optional[int] = None) -> List[Dict[str, Any]]:
        """获取提交趋势数据"""
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)

        # 基础查询
        base_query = self.db.query(ExperimentRecord).join(Student, ExperimentRecord.student_id == Student.id)
        if class_id:
            base_query = base_query.filter(Student.class_id == class_id)

        # 按日期分组统计
        daily_stats = base_query.filter(
            ExperimentRecord.submitted_at >= start_date,
            ExperimentRecord.submitted_at <= end_date
        ).with_entities(
            func.date(ExperimentRecord.submitted_at).label('date'),
            func.count(ExperimentRecord.id).label('total_submissions'),
            func.sum(case((ExperimentRecord.is_passed == True, 1), else_=0)).label('passed_submissions')
        ).group_by(func.date(ExperimentRecord.submitted_at)).all()

        # 填充缺失日期
        trends = []
        current_date = start_date.date()
        stats_dict = {stat.date: stat for stat in daily_stats}

        while current_date <= end_date.date():
            stat = stats_dict.get(current_date)
            trends.append({
                "date": current_date.isoformat(),
                "total_submissions": int(stat.total_submissions) if stat else 0,
                "passed_submissions": int(stat.passed_submissions) if stat else 0,
                "pass_rate": round((stat.passed_submissions / stat.total_submissions * 100), 2) if stat and stat.total_submissions > 0 else 0
            })
            current_date += timedelta(days=1)

        return trends