"""
应用配置管理
"""

from pydantic_settings import BaseSettings
from typing import List
import os


class Settings(BaseSettings):
    """应用配置类"""
    
    # 应用基础配置
    APP_NAME: str = "大学物理实验"
    APP_VERSION: str = "1.0.0"
    DEBUG: bool = False
    
    # 数据库配置 - 使用SQLite进行开发测试
    DATABASE_URL: str = "sqlite:///./physics_experiments.db"
    DB_HOST: str = "localhost"
    DB_PORT: int = 3306
    DB_USER: str = "root"
    DB_PASSWORD: str = ""
    DB_NAME: str = "physics_experiments"
    DB_POOL_SIZE: int = 10
    DB_MAX_OVERFLOW: int = 20
    DB_POOL_TIMEOUT: int = 30
    
    # JWT配置
    SECRET_KEY: str = "your-secret-key-change-in-production"
    JWT_SECRET_KEY: str = "your-jwt-secret-key-change-in-production"
    JWT_ALGORITHM: str = "HS256"
    JWT_EXPIRE_MINUTES: int = 1440  # 24小时
    
    # CORS配置
    ALLOWED_ORIGINS: List[str] = [
        "http://localhost:3000",
        "http://localhost:5173",
        "http://localhost:5174",
        "http://127.0.0.1:3000",
        "http://127.0.0.1:5173",
        "http://127.0.0.1:5174",
        "http://localhost:30002",
        "http://127.0.0.1:30002",
        "http://localhost:30003",
        "http://127.0.0.1:30003",
        "http://**************:30002",
        "http://**************:30003"
    ]
    ALLOWED_HOSTS: List[str] = ["*"]
    
    # AI服务配置
    AI_SERVICE_URL: str = "http://localhost:8001"
    AI_SERVICE_API_KEY: str = ""
    AI_ANALYSIS_ENABLED: bool = True
    
    # 文件上传配置
    UPLOAD_MAX_SIZE: int = 10485760  # 10MB
    UPLOAD_ALLOWED_EXTENSIONS: List[str] = ["png", "jpg", "jpeg", "gif", "pdf"]
    UPLOAD_DIR: str = "uploads"
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "logs/app.log"
    
    # 实验配置
    MAX_SUBMISSION_ATTEMPTS: int = 3
    PLOT_IMAGE_MAX_SIZE: int = 5242880  # 5MB
    SESSION_TIMEOUT_MINUTES: int = 120
    
    @property
    def database_url(self) -> str:
        """构建数据库连接URL"""
        # 如果设置了DATABASE_URL，直接使用（用于SQLite等）
        if hasattr(self, 'DATABASE_URL') and self.DATABASE_URL:
            return self.DATABASE_URL
        # 否则使用MySQL配置
        return f"mysql+pymysql://{self.DB_USER}:{self.DB_PASSWORD}@{self.DB_HOST}:{self.DB_PORT}/{self.DB_NAME}?charset=utf8mb4&use_unicode=1&collation=utf8mb4_unicode_ci"
    
    @property
    def async_database_url(self) -> str:
        """构建异步数据库连接URL"""
        return f"mysql+aiomysql://{self.DB_USER}:{self.DB_PASSWORD}@{self.DB_HOST}:{self.DB_PORT}/{self.DB_NAME}?charset=utf8mb4&use_unicode=1&collation=utf8mb4_unicode_ci"
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True


# 创建全局配置实例
settings = Settings()

# 确保上传目录存在
os.makedirs(settings.UPLOAD_DIR, exist_ok=True)
os.makedirs(os.path.dirname(settings.LOG_FILE), exist_ok=True)
