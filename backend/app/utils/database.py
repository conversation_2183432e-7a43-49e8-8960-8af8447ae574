"""
数据库连接和会话管理
"""

from sqlalchemy import create_engine, MetaData, text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import QueuePool
from typing import Generator
import logging

from app.utils.config import settings

logger = logging.getLogger(__name__)

# 创建数据库引擎
engine = create_engine(
    settings.database_url,
    poolclass=QueuePool,
    pool_size=settings.DB_POOL_SIZE,
    max_overflow=settings.DB_MAX_OVERFLOW,
    pool_timeout=settings.DB_POOL_TIMEOUT,
    pool_pre_ping=True,  # 连接前检查连接是否有效
    echo=settings.DEBUG,  # 在调试模式下打印SQL语句
    connect_args={
        "charset": "utf8mb4",
        "init_command": "SET character_set_client=utf8mb4,character_set_connection=utf8mb4,character_set_results=utf8mb4,collation_connection=utf8mb4_unicode_ci"
    }
)

# 创建会话工厂
SessionLocal = sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=engine
)

# 创建基础模型类
Base = declarative_base()

# 元数据对象
metadata = MetaData()


async def init_db():
    """初始化数据库连接并确保表与最小数据存在"""
    try:
        # 测试数据库连接
        with engine.connect() as conn:
            conn.execute(text("SELECT 1"))
        logger.info("数据库连接测试成功")

        # 确保模型已导入，再创建表
        try:
            # 导入模型以注册到元数据
            from app.models import experiment as _exp_models  # noqa: F401
            from app.models import user as _user_models      # noqa: F401
            from app.models import system as _sys_models     # noqa: F401
            from app.models.base import Base as ModelsBase

            ModelsBase.metadata.create_all(bind=engine)
            logger.info("数据库表检查/创建完成")
        except Exception as e:
            logger.error(f"创建数据库表失败: {e}")
            raise

        # 最小数据种子：确保当前实验样板存在
        try:
            from app.models.experiment import ExperimentType
            db = SessionLocal()
            try:
                exists = db.query(ExperimentType).filter(ExperimentType.code == 'current_control_circuit').first()
                if not exists:
                    et = ExperimentType(
                        name='制流电路实验',
                        code='current_control_circuit',
                        description='样板实验：制流电路实验',
                        instructions='按照页面步骤完成实验数据采集、分析与提交',
                        duration_minutes=120,
                        max_score=100.0,
                        is_active=True
                    )
                    db.add(et)
                    db.commit()
                    logger.info("已写入默认实验类型 current_control_circuit")
            finally:
                db.close()
        except Exception as e:
            logger.warning(f"写入默认实验类型失败（可忽略）: {e}")

    except Exception as e:
        logger.error(f"数据库连接失败: {e}")
        raise


async def close_db():
    """关闭数据库连接"""
    try:
        engine.dispose()
        logger.info("数据库连接池已关闭")
    except Exception as e:
        logger.error(f"关闭数据库连接时出错: {e}")


def get_db() -> Generator[Session, None, None]:
    """
    获取数据库会话的依赖注入函数
    用于FastAPI的Depends
    """
    db = SessionLocal()
    try:
        yield db
    except Exception as e:
        logger.error(f"数据库会话错误: {e}")
        db.rollback()
        raise
    finally:
        db.close()


class DatabaseManager:
    """数据库管理器类"""
    
    def __init__(self):
        self.engine = engine
        self.SessionLocal = SessionLocal
    
    def create_tables(self):
        """创建所有表"""
        try:
            Base.metadata.create_all(bind=self.engine)
            logger.info("数据库表创建成功")
        except Exception as e:
            logger.error(f"创建数据库表失败: {e}")
            raise
    
    def drop_tables(self):
        """删除所有表"""
        try:
            Base.metadata.drop_all(bind=self.engine)
            logger.info("数据库表删除成功")
        except Exception as e:
            logger.error(f"删除数据库表失败: {e}")
            raise
    
    def get_session(self) -> Session:
        """获取数据库会话"""
        return self.SessionLocal()
    
    def execute_raw_sql(self, sql: str, params: dict = None):
        """执行原生SQL"""
        with self.engine.connect() as conn:
            if params:
                result = conn.execute(sql, params)
            else:
                result = conn.execute(sql)
            return result


# 创建全局数据库管理器实例
db_manager = DatabaseManager()
