"""
实验相关数据模型
"""

from sqlalchemy import Column, Integer, String, Boolean, ForeignKey, Text, JSON, DECIMAL, DateTime, Enum
from sqlalchemy.orm import relationship
import enum

from .base import BaseModel


class ExperimentStatus(enum.Enum):
    """实验状态枚举"""
    SUBMITTED = "submitted"
    REVIEWED = "reviewed"
    APPROVED = "approved"
    REJECTED = "rejected"
    COMPLETED = "completed"


class ExperimentType(BaseModel):
    """实验类型模型"""
    
    __tablename__ = "experiment_types"
    
    name = Column(String(100), nullable=False, comment="实验名称")
    code = Column(String(50), unique=True, nullable=False, comment="实验代码")
    description = Column(Text, comment="实验描述")
    instructions = Column(Text, comment="实验说明")
    duration_minutes = Column(Integer, default=120, comment="预计实验时长(分钟)")
    max_score = Column(DECIMAL(5, 2), default=100.00, comment="满分")
    is_active = Column(<PERSON><PERSON>an, default=True, comment="是否启用")
    
    # 关系
    experiment_records = relationship("ExperimentRecord", back_populates="experiment_type")
    
    def __repr__(self):
        return f"<ExperimentType(id={self.id}, name='{self.name}', code='{self.code}')>"


class ExperimentRecord(BaseModel):
    """实验记录模型"""
    
    __tablename__ = "experiment_records"
    
    student_id = Column(Integer, ForeignKey("students.id"), nullable=True, comment="学生ID")
    experiment_type_id = Column(Integer, ForeignKey("experiment_types.id"), nullable=False, comment="实验类型ID")
    submission_data = Column(JSON, nullable=False, comment="提交的实验数据(包含图形数据和AI分析结果)")
    is_passed = Column(Boolean, comment="是否通过")
    score = Column(DECIMAL(5, 2), comment="得分")
    submitted_at = Column(DateTime, comment="提交时间")
    reviewed_at = Column(DateTime, comment="审核时间")
    reviewed_by = Column(Integer, ForeignKey("admins.id"), comment="审核人ID")
    review_comments = Column(Text, comment="审核意见")
    status = Column(
        Enum(ExperimentStatus, values_callable=lambda obj: [e.value for e in obj]),
        default=ExperimentStatus.SUBMITTED.value,
        comment="状态"
    )
    
    # 关系
    student = relationship("Student", back_populates="experiment_records")
    experiment_type = relationship("ExperimentType", back_populates="experiment_records")
    reviewer = relationship("Admin", back_populates="reviewed_records")
    
    def __repr__(self):
        return f"<ExperimentRecord(id={self.id}, student_id={self.student_id}, experiment_type_id={self.experiment_type_id}, status='{self.status.value}')>"
