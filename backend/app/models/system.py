"""
系统配置相关数据模型
"""

from sqlalchemy import Column, String, Text

from .base import BaseModel


class SystemConfig(BaseModel):
    """系统配置模型"""
    
    __tablename__ = "system_configs"
    
    config_key = Column(String(100), unique=True, nullable=False, comment="配置键")
    config_value = Column(Text, comment="配置值")
    description = Column(String(255), comment="配置描述")
    
    def __repr__(self):
        return f"<SystemConfig(id={self.id}, key='{self.config_key}', value='{self.config_value}')>"
