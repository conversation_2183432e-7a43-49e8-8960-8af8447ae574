"""
用户相关数据模型
"""

from sqlalchemy import Column, Integer, String, Boolean, ForeignKey, Text, Enum
from sqlalchemy.orm import relationship
import enum

from .base import BaseModel


class UserRole(enum.Enum):
    """用户角色枚举"""
    SUPER_ADMIN = "super_admin"
    ADMIN = "admin"
    TEACHER = "teacher"


class Class(BaseModel):
    """班级模型"""
    
    __tablename__ = "classes"
    
    name = Column(String(100), nullable=False, comment="班级名称")
    code = Column(String(50), unique=True, nullable=False, comment="班级代码")
    department = Column(String(100), comment="院系")
    grade = Column(Integer, comment="年级")
    
    # 关系
    students = relationship("Student", back_populates="class_info", cascade="all, delete-orphan")
    groups = relationship("Group", back_populates="class_info", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Class(id={self.id}, name='{self.name}', code='{self.code}')>"


class Group(BaseModel):
    """分组模型"""
    
    __tablename__ = "groups"
    
    name = Column(String(100), nullable=False, comment="分组名称")
    class_id = Column(Integer, ForeignKey("classes.id"), nullable=False, comment="所属班级ID")
    description = Column(Text, comment="分组描述")
    
    # 关系
    class_info = relationship("Class", back_populates="groups")
    students = relationship("Student", back_populates="group_info")
    
    def __repr__(self):
        return f"<Group(id={self.id}, name='{self.name}', class_id={self.class_id})>"


class Student(BaseModel):
    """学生模型"""
    
    __tablename__ = "students"
    
    student_id = Column(String(20), unique=True, nullable=False, comment="学号")
    name = Column(String(50), nullable=False, comment="姓名")
    class_id = Column(Integer, ForeignKey("classes.id"), nullable=False, comment="班级ID")
    group_id = Column(Integer, ForeignKey("groups.id"), comment="分组ID")
    email = Column(String(100), comment="邮箱")
    phone = Column(String(20), comment="电话")
    password_hash = Column(String(255), comment="密码哈希")
    is_active = Column(Boolean, default=True, comment="是否激活")
    
    # 关系
    class_info = relationship("Class", back_populates="students")
    group_info = relationship("Group", back_populates="students")
    experiment_records = relationship("ExperimentRecord", back_populates="student", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Student(id={self.id}, student_id='{self.student_id}', name='{self.name}')>"


class Admin(BaseModel):
    """管理员模型"""
    
    __tablename__ = "admins"
    
    username = Column(String(50), unique=True, nullable=False, comment="用户名")
    name = Column(String(50), nullable=False, comment="姓名")
    email = Column(String(100), comment="邮箱")
    password_hash = Column(String(255), nullable=False, comment="密码哈希")
    role = Column(Enum(UserRole), default=UserRole.TEACHER, comment="角色")
    is_active = Column(Boolean, default=True, comment="是否激活")
    
    # 关系
    reviewed_records = relationship("ExperimentRecord", back_populates="reviewer")
    
    def __repr__(self):
        return f"<Admin(id={self.id}, username='{self.username}', name='{self.name}', role='{self.role.value}')>"


class UnassignedStudent(BaseModel):
    """未分班分组学生模型"""

    __tablename__ = "unassigned_students"

    student_id = Column(String(20), unique=True, nullable=False, comment="学号")
    name = Column(String(50), nullable=False, comment="姓名")
    email = Column(String(100), comment="邮箱")
    phone = Column(String(20), comment="电话")
    department = Column(String(100), comment="院系")
    grade = Column(Integer, comment="年级")
    is_processed = Column(Boolean, default=False, comment="是否已处理")
    notes = Column(Text, comment="备注")

    def __repr__(self):
        return f"<UnassignedStudent(id={self.id}, student_id='{self.student_id}', name='{self.name}')>"
