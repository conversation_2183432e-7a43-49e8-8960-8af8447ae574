"""
学生相关API路由
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from pydantic import BaseModel
from typing import List, Optional
import logging

from app.utils.database import get_db
from app.utils.auth import get_current_user
from app.models.user import Student, Class, Group
from app.services.student import StudentService

logger = logging.getLogger(__name__)

router = APIRouter()


class StudentResponse(BaseModel):
    """学生响应模型"""
    id: int
    student_id: str
    name: str
    class_name: Optional[str]
    group_name: Optional[str]
    email: Optional[str]
    is_active: bool


@router.get("/profile", response_model=StudentResponse)
async def get_student_profile(
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """获取学生个人信息"""
    try:
        if current_user.get("user_type") != "student":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="只有学生可以访问此接口"
            )
        
        student_service = StudentService(db)
        student = student_service.get_student_by_id(current_user["id"])
        
        if not student:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="学生信息不存在"
            )
        
        return StudentResponse(
            id=student.id,
            student_id=student.student_id,
            name=student.name,
            class_name=student.class_info.name if student.class_info else None,
            group_name=student.group_info.name if student.group_info else None,
            email=student.email,
            is_active=student.is_active
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取学生信息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取学生信息失败"
        )


@router.get("/classmates", response_model=List[StudentResponse])
async def get_classmates(
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """获取同班同学列表"""
    try:
        if current_user.get("user_type") != "student":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="只有学生可以访问此接口"
            )
        
        student_service = StudentService(db)
        current_student = student_service.get_student_by_id(current_user["id"])
        
        if not current_student:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="学生信息不存在"
            )
        
        classmates = student_service.get_classmates(current_student.class_id)
        
        return [
            StudentResponse(
                id=student.id,
                student_id=student.student_id,
                name=student.name,
                class_name=student.class_info.name if student.class_info else None,
                group_name=student.group_info.name if student.group_info else None,
                email=student.email,
                is_active=student.is_active
            )
            for student in classmates
        ]
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取同班同学失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取同班同学失败"
        )
