"""
认证相关API路由
"""

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from pydantic import BaseModel
from typing import Optional
import logging

from app.utils.database import get_db
from app.utils.auth import verify_password, create_access_token, decode_access_token
from app.models.user import Student, Admin
from app.services.auth import AuthService

logger = logging.getLogger(__name__)

router = APIRouter()
security = HTTPBearer()


class LoginRequest(BaseModel):
    """登录请求模型"""
    username: str
    password: str
    user_type: str  # "student" 或 "admin"


class LoginResponse(BaseModel):
    """登录响应模型"""
    access_token: str
    token_type: str = "bearer"
    user_info: dict


class UserInfo(BaseModel):
    """用户信息模型"""
    id: int
    name: str
    user_type: str
    additional_info: Optional[dict] = None


@router.post("/login", response_model=LoginResponse)
async def login(
    request: LoginRequest,
    db: Session = Depends(get_db)
):
    """用户登录"""
    try:
        auth_service = AuthService(db)
        
        if request.user_type == "student":
            user = auth_service.authenticate_student(request.username, request.password)
            if not user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="学号或密码错误"
                )
            
            user_info = {
                "id": user.id,
                "student_id": user.student_id,
                "name": user.name,
                "class_id": user.class_id,
                "group_id": user.group_id,
                "user_type": "student"
            }
            
        elif request.user_type == "admin":
            user = auth_service.authenticate_admin(request.username, request.password)
            if not user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="用户名或密码错误"
                )
            
            user_info = {
                "id": user.id,
                "username": user.username,
                "name": user.name,
                "role": user.role.value,
                "user_type": "admin"
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的用户类型"
            )
        
        # 创建访问令牌
        access_token = create_access_token(
            data={"sub": str(user.id), "user_type": request.user_type}
        )
        
        return LoginResponse(
            access_token=access_token,
            user_info=user_info
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"登录失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="登录服务暂时不可用"
        )


@router.get("/me", response_model=UserInfo)
async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
):
    """获取当前用户信息"""
    try:
        # 解码JWT令牌
        payload = decode_access_token(credentials.credentials)
        user_id = int(payload.get("sub"))
        user_type = payload.get("user_type")
        
        if user_type == "student":
            user = db.query(Student).filter(Student.id == user_id).first()
            if not user:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="用户不存在"
                )
            
            return UserInfo(
                id=user.id,
                name=user.name,
                user_type="student",
                additional_info={
                    "student_id": user.student_id,
                    "class_id": user.class_id,
                    "group_id": user.group_id
                }
            )
            
        elif user_type == "admin":
            user = db.query(Admin).filter(Admin.id == user_id).first()
            if not user:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="用户不存在"
                )
            
            return UserInfo(
                id=user.id,
                name=user.name,
                user_type="admin",
                additional_info={
                    "username": user.username,
                    "role": user.role.value
                }
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的用户类型"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取用户信息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的访问令牌"
        )


@router.post("/logout")
async def logout():
    """用户登出"""
    # JWT是无状态的，客户端删除token即可
    return {"message": "登出成功"}
