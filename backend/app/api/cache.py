"""
缓存相关API接口
"""
from fastapi import APIRouter, HTTPException, Request, Response
from pydantic import BaseModel
from typing import Dict, Any, Optional
import uuid

from ..services.cache import experiment_cache

router = APIRouter(prefix="/api/cache", tags=["缓存"])


class CacheDataRequest(BaseModel):
    """缓存数据请求模型"""
    experiment_type: str
    data: Dict[str, Any]
    ttl: Optional[int] = None


class CacheDataResponse(BaseModel):
    """缓存数据响应模型"""
    success: bool
    message: str
    session_id: Optional[str] = None


class GetCacheResponse(BaseModel):
    """获取缓存响应模型"""
    success: bool
    data: Optional[Dict[str, Any]] = None
    message: str


def get_or_create_session_id(request: Request, response: Response) -> str:
    """获取或创建会话ID"""
    session_id = request.cookies.get("experiment_session_id")
    if not session_id:
        session_id = str(uuid.uuid4())
        response.set_cookie(
            key="experiment_session_id",
            value=session_id,
            max_age=7200,  # 2小时
            httponly=True,
            samesite="lax"
        )
    return session_id


@router.post("/save", response_model=CacheDataResponse)
async def save_experiment_cache(
    cache_request: CacheDataRequest,
    request: Request,
    response: Response
):
    """
    保存实验数据到缓存
    """
    try:
        session_id = get_or_create_session_id(request, response)
        
        success = experiment_cache.save_experiment_data(
            session_id=session_id,
            experiment_type=cache_request.experiment_type,
            data=cache_request.data,
            ttl=cache_request.ttl
        )
        
        if success:
            return CacheDataResponse(
                success=True,
                message="数据缓存成功",
                session_id=session_id
            )
        else:
            raise HTTPException(status_code=500, detail="数据缓存失败")
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"缓存保存错误: {str(e)}")


@router.get("/get/{experiment_type}", response_model=GetCacheResponse)
async def get_experiment_cache(
    experiment_type: str,
    request: Request,
    response: Response
):
    """
    获取缓存的实验数据
    """
    try:
        session_id = get_or_create_session_id(request, response)
        
        cached_data = experiment_cache.get_experiment_data(
            session_id=session_id,
            experiment_type=experiment_type
        )
        
        if cached_data is not None:
            return GetCacheResponse(
                success=True,
                data=cached_data,
                message="缓存数据获取成功"
            )
        else:
            return GetCacheResponse(
                success=False,
                data=None,
                message="未找到缓存数据"
            )
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"缓存获取错误: {str(e)}")


@router.delete("/delete/{experiment_type}")
async def delete_experiment_cache(
    experiment_type: str,
    request: Request,
    response: Response
):
    """
    删除缓存的实验数据
    """
    try:
        session_id = get_or_create_session_id(request, response)
        
        success = experiment_cache.delete_experiment_data(
            session_id=session_id,
            experiment_type=experiment_type
        )
        
        if success:
            return {"success": True, "message": "缓存数据删除成功"}
        else:
            raise HTTPException(status_code=500, detail="缓存数据删除失败")
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"缓存删除错误: {str(e)}")


@router.get("/info")
async def get_cache_info(
    request: Request,
    response: Response
):
    """
    获取当前会话的缓存信息
    """
    try:
        session_id = get_or_create_session_id(request, response)
        
        cache_info = experiment_cache.get_cache_info(session_id)
        
        return {
            "success": True,
            "data": cache_info,
            "message": "缓存信息获取成功"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"缓存信息获取错误: {str(e)}")
