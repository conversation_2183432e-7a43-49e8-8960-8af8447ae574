"""
管理员相关API路由
"""

from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
import logging
import csv
import io
import json

from app.utils.database import get_db
# from app.utils.auth import require_admin  # 移除权限验证，系统无登录
# from app.models.user import Student, Admin, Class, Group  # not needed here
from app.models.experiment import ExperimentRecord, ExperimentType
from app.models.user import UnassignedStudent, Student, Class, Group
from app.services.admin import AdminService

logger = logging.getLogger(__name__)

router = APIRouter()


class StudentManagementResponse(BaseModel):
    """学生管理响应模型"""
    id: int
    student_id: str
    name: str
    class_name: str
    group_name: Optional[str]
    email: Optional[str]
    is_active: bool
    experiment_count: int


class ClassResponse(BaseModel):
    """班级响应模型"""
    id: int
    name: str
    code: str
    department: Optional[str]
    grade: Optional[int]
    student_count: int


class ClassCreateRequest(BaseModel):
    """创建班级请求模型"""
    name: str
    code: str
    department: Optional[str] = None
    grade: Optional[int] = None


class ClassUpdateRequest(BaseModel):
    """更新班级请求模型"""
    name: Optional[str] = None
    code: Optional[str] = None
    department: Optional[str] = None
    grade: Optional[int] = None


class StudentCreateRequest(BaseModel):
    """创建学生请求模型"""
    student_id: str
    name: str
    class_id: int
    group_id: Optional[int] = None
    email: Optional[str] = None


class StudentUpdateRequest(BaseModel):
    """更新学生请求模型"""
    student_id: Optional[str] = None
    name: Optional[str] = None
    class_id: Optional[int] = None
    group_id: Optional[int] = None
    email: Optional[str] = None
    is_active: Optional[bool] = None


class StudentImportResponse(BaseModel):
    """学生导入响应模型"""
    success_count: int
    error_count: int
    errors: List[str]


class StudentDetailResponse(BaseModel):
    """学生详情响应模型"""
    id: int
    student_id: str
    name: str
    class_name: Optional[str]
    group_name: Optional[str]
    email: Optional[str]
    is_active: bool
    experiment_count: int
    is_unassigned: bool = False


class ExperimentRecordResponse(BaseModel):
    """实验记录响应模型"""
    id: int
    experiment_name: str
    experiment_code: str
    submitted_at: str
    status: str
    is_passed: Optional[bool]
    score: Optional[float]
    submission_data: Optional[Dict[str, Any]]
    plot_data: Optional[str]
    analysis_result: Optional[str]


class CurrentControlCircuitResponse(BaseModel):
    """制流电路实验结果响应模型"""
    id: int
    student_name: str
    student_id: str
    submitted_at: str
    is_passed: Optional[bool]
    score: Optional[float]

    # 实验数据
    voltage_data: Optional[List[float]]
    current_data: Optional[List[float]]
    resistance_values: Optional[List[float]]
    temperature: Optional[float]
    humidity: Optional[float]

    # 计算结果
    calculated_resistance: Optional[float]
    theoretical_resistance: Optional[float]
    error_percentage: Optional[float]

    # 图形数据
    plot_data: Optional[str]
    plot_json: Optional[str]

    # 分析结果
    analysis_result: Optional[str]
    conclusions: Optional[List[str]]


class ExperimentStatistics(BaseModel):
    """实验统计模型"""
    experiment_name: str
    total_submissions: int
    passed_count: int
    failed_count: int
    pending_count: int
    pass_rate: float


class RecentActivity(BaseModel):
    """最近活动记录"""
    id: int
    submitted_at: Optional[str]
    student_no: Optional[str]
    student_name: Optional[str]
    experiment_name: Optional[str]
    experiment_code: Optional[str]
    status: Optional[str]
    is_passed: Optional[bool]
    score: Optional[float]


class OverviewStats(BaseModel):
    """管理员仪表盘概览统计"""
    total_students: int
    total_classes: int
    total_experiments: int
    total_submissions: int
    passed_count: int
    failed_count: int
    pending_count: int
    pass_rate: float
    recent_activities: List[RecentActivity]

class LearningProgressStats(BaseModel):
    """学习进度统计模型"""
    class_stats: List[dict]
    group_stats: List[dict]
    student_stats: List[dict]
    experiment_stats: List[dict]


class SubmissionTrend(BaseModel):
    """提交趋势模型"""
    date: str
    total_submissions: int
    passed_submissions: int
    pass_rate: float


@router.get("/students", response_model=List[StudentManagementResponse])
async def get_all_students(
    class_id: Optional[int] = None,
    include_unassigned: bool = False,
    db: Session = Depends(get_db)
):
    """获取所有学生信息"""
    try:
        admin_service = AdminService(db)
        students = admin_service.get_students(class_id=class_id)

        result = [
            StudentManagementResponse(
                id=student.id,
                student_id=student.student_id,
                name=student.name,
                class_name=student.class_info.name,
                group_name=student.group_info.name if student.group_info else None,
                email=student.email,
                is_active=student.is_active,
                experiment_count=len(student.experiment_records)
            )
            for student in students
        ]

        # 如果需要包含未分班学生
        if include_unassigned:
            unassigned_students = db.query(UnassignedStudent).all()
            for unassigned in unassigned_students:
                # 计算实验记录数量（通过submission_data中的student_id匹配）
                experiment_count = db.query(ExperimentRecord).filter(
                    ExperimentRecord.submission_data.like(f'%"student_id": "{unassigned.student_id}"%')
                ).count()

                result.append(StudentManagementResponse(
                    id=unassigned.id + 10000,  # 使用偏移避免ID冲突
                    student_id=unassigned.student_id,
                    name=unassigned.name,
                    class_name="无",
                    group_name=None,
                    email=unassigned.email,
                    is_active=not unassigned.is_processed,
                    experiment_count=experiment_count
                ))

        return result

    except Exception as e:
        logger.error(f"获取学生列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取学生列表失败"
        )


@router.get("/classes", response_model=List[ClassResponse])
async def get_all_classes(
    db: Session = Depends(get_db)
):
    """获取所有班级信息"""
    try:
        admin_service = AdminService(db)
        classes = admin_service.get_classes()

        return [
            ClassResponse(
                id=cls.id,
                name=cls.name,
                code=cls.code,
                department=cls.department,
                grade=cls.grade,
                student_count=len(cls.students)
            )
            for cls in classes
        ]

    except Exception as e:
        logger.error(f"获取班级列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取班级列表失败"
        )


@router.post("/classes", response_model=ClassResponse)
async def create_class(
    class_data: ClassCreateRequest,
    db: Session = Depends(get_db)
):
    """创建新班级"""
    try:
        admin_service = AdminService(db)
        new_class = admin_service.create_class(
            name=class_data.name,
            code=class_data.code,
            department=class_data.department,
            grade=class_data.grade
        )

        return ClassResponse(
            id=new_class.id,
            name=new_class.name,
            code=new_class.code,
            department=new_class.department,
            grade=new_class.grade,
            student_count=0
        )

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"创建班级失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="创建班级失败"
        )


@router.put("/classes/{class_id}", response_model=ClassResponse)
async def update_class(
    class_id: int,
    class_data: ClassUpdateRequest,
    db: Session = Depends(get_db)
):
    """更新班级信息"""
    try:
        admin_service = AdminService(db)
        updated_class = admin_service.update_class(
            class_id=class_id,
            name=class_data.name,
            code=class_data.code,
            department=class_data.department,
            grade=class_data.grade
        )

        return ClassResponse(
            id=updated_class.id,
            name=updated_class.name,
            code=updated_class.code,
            department=updated_class.department,
            grade=updated_class.grade,
            student_count=len(updated_class.students)
        )

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"更新班级失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新班级失败"
        )


@router.delete("/classes/{class_id}")
async def delete_class(
    class_id: int,
    db: Session = Depends(get_db)
):
    """删除班级"""
    try:
        admin_service = AdminService(db)
        admin_service.delete_class(class_id)

        return {"message": "班级删除成功"}

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"删除班级失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="删除班级失败"
        )


@router.post("/students", response_model=StudentManagementResponse)
async def create_student(
    student_data: StudentCreateRequest,
    db: Session = Depends(get_db)
):
    """创建新学生"""
    try:
        admin_service = AdminService(db)
        new_student = admin_service.create_student(
            student_id=student_data.student_id,
            name=student_data.name,
            class_id=student_data.class_id,
            group_id=student_data.group_id,
            email=student_data.email
        )

        return StudentManagementResponse(
            id=new_student.id,
            student_id=new_student.student_id,
            name=new_student.name,
            class_name=new_student.class_info.name,
            group_name=new_student.group_info.name if new_student.group_info else None,
            email=new_student.email,
            is_active=new_student.is_active,
            experiment_count=len(new_student.experiment_records)
        )

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"创建学生失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="创建学生失败"
        )


@router.put("/students/{student_id}", response_model=StudentManagementResponse)
async def update_student(
    student_id: int,
    student_data: StudentUpdateRequest,
    db: Session = Depends(get_db)
):
    """更新学生信息"""
    try:
        admin_service = AdminService(db)
        updated_student = admin_service.update_student(
            student_id=student_id,
            student_no=student_data.student_id,
            name=student_data.name,
            class_id=student_data.class_id,
            group_id=student_data.group_id,
            email=student_data.email,
            is_active=student_data.is_active
        )

        return StudentManagementResponse(
            id=updated_student.id,
            student_id=updated_student.student_id,
            name=updated_student.name,
            class_name=updated_student.class_info.name,
            group_name=updated_student.group_info.name if updated_student.group_info else None,
            email=updated_student.email,
            is_active=updated_student.is_active,
            experiment_count=len(updated_student.experiment_records)
        )

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"更新学生失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新学生失败"
        )


@router.delete("/students/{student_id}")
async def delete_student(
    student_id: int,
    db: Session = Depends(get_db)
):
    """删除学生"""
    try:
        admin_service = AdminService(db)
        admin_service.delete_student(student_id)

        return {"message": "学生删除成功"}

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"删除学生失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="删除学生失败"
        )


@router.post("/students/import", response_model=StudentImportResponse)
async def import_students(
    file: UploadFile = File(...),
    db: Session = Depends(get_db)
):
    """批量导入学生"""
    try:
        if not file.filename.endswith('.csv'):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="只支持CSV文件格式"
            )

        content = await file.read()
        csv_content = content.decode('utf-8')

        admin_service = AdminService(db)
        result = admin_service.import_students_from_csv(csv_content)

        return result

    except Exception as e:
        logger.error(f"导入学生失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"导入学生失败: {str(e)}"
        )


@router.get("/statistics/overview", response_model=OverviewStats)
async def get_overview_stats(
    db: Session = Depends(get_db)
):
    """获取概览统计信息"""
    try:
        admin_service = AdminService(db)
        overview = admin_service.get_overview_stats()
        return overview
    except Exception as e:
        logger.error(f"获取概览统计失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取概览统计失败"
        )



@router.get("/statistics/experiments", response_model=List[ExperimentStatistics])
async def get_experiment_statistics(
    db: Session = Depends(get_db)
):
    """获取实验统计信息"""
    try:
        admin_service = AdminService(db)
        statistics = admin_service.get_experiment_statistics()

        return statistics

    except Exception as e:
        logger.error(f"获取实验统计失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取实验统计失败"
        )


@router.get("/records")
async def get_all_experiment_records(
    page: int = 1,
    page_size: int = 20,
    experiment_code: Optional[str] = None,
    student_no: Optional[str] = None,
    student_name: Optional[str] = None,
    date_from: Optional[str] = None,
    date_to: Optional[str] = None,
    status: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """获取实验记录（支持筛选与分页）"""
    try:
        admin_service = AdminService(db)
        from datetime import datetime
        df = datetime.fromisoformat(date_from) if date_from else None
        dt = datetime.fromisoformat(date_to) if date_to else None
        items, total = admin_service.query_experiment_records(
            page=page,
            page_size=page_size,
            experiment_code=experiment_code,
            student_no=student_no,
            student_name=student_name,
            date_from=df,
            date_to=dt,
            status=status,
        )
        return {"items": items, "total": total, "page": page, "page_size": page_size}
    except Exception as e:
        logger.error(f"获取实验记录失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取实验记录失败"
        )


@router.get("/records/{record_id}")
async def get_record_detail(
    record_id: int,
    db: Session = Depends(get_db)
):
    """获取单条实验记录详情"""
    try:
        rec = db.query(ExperimentRecord).filter(ExperimentRecord.id == record_id).first()
        if not rec:
            raise HTTPException(status_code=404, detail="实验记录不存在")
        return rec.to_dict()
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取实验记录详情失败: {e}")
        raise HTTPException(status_code=500, detail="获取实验记录详情失败")


@router.get("/students/{student_id}/detail", response_model=StudentDetailResponse)
async def get_student_detail(
    student_id: int,
    db: Session = Depends(get_db)
):
    """获取学生详情"""
    try:
        # 先尝试从正式学生表查找
        admin_service = AdminService(db)
        student = db.query(Student).filter(Student.id == student_id).first()

        if student:
            return StudentDetailResponse(
                id=student.id,
                student_id=student.student_id,
                name=student.name,
                class_name=student.class_info.name,
                group_name=student.group_info.name if student.group_info else None,
                email=student.email,
                is_active=student.is_active,
                experiment_count=len(student.experiment_records),
                is_unassigned=False
            )

        # 如果不在正式学生表中，检查未分班学生表
        unassigned_id = student_id - 10000  # 减去偏移
        unassigned = db.query(UnassignedStudent).filter(UnassignedStudent.id == unassigned_id).first()

        if unassigned:
            # 计算实验记录数量
            experiment_count = db.query(ExperimentRecord).filter(
                ExperimentRecord.submission_data.like(f'%"student_id": "{unassigned.student_id}"%')
            ).count()

            return StudentDetailResponse(
                id=student_id,
                student_id=unassigned.student_id,
                name=unassigned.name,
                class_name="无",
                group_name=None,
                email=unassigned.email,
                is_active=not unassigned.is_processed,
                experiment_count=experiment_count,
                is_unassigned=True
            )

        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="学生不存在"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取学生详情失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取学生详情失败"
        )


@router.get("/students/{student_id}/records", response_model=List[ExperimentRecordResponse])
async def get_student_experiment_records(
    student_id: int,
    experiment_code: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """获取学生的实验记录"""
    try:
        records = []

        # 先尝试从正式学生表查找
        student = db.query(Student).filter(Student.id == student_id).first()

        if student:
            # 正式学生的记录
            query = db.query(ExperimentRecord).filter(ExperimentRecord.student_id == student_id)
            if experiment_code:
                experiment_type = db.query(ExperimentType).filter(ExperimentType.code == experiment_code).first()
                if experiment_type:
                    query = query.filter(ExperimentRecord.experiment_type_id == experiment_type.id)

            records = query.order_by(ExperimentRecord.submitted_at.desc()).all()
        else:
            # 检查未分班学生
            unassigned_id = student_id - 10000
            unassigned = db.query(UnassignedStudent).filter(UnassignedStudent.id == unassigned_id).first()

            if unassigned:
                # 通过submission_data查找记录
                query = db.query(ExperimentRecord).filter(
                    ExperimentRecord.submission_data.like(f'%"student_id": "{unassigned.student_id}"%')
                )
                if experiment_code:
                    experiment_type = db.query(ExperimentType).filter(ExperimentType.code == experiment_code).first()
                    if experiment_type:
                        query = query.filter(ExperimentRecord.experiment_type_id == experiment_type.id)

                records = query.order_by(ExperimentRecord.submitted_at.desc()).all()

        # 转换为响应格式
        result = []
        for record in records:
            experiment_type = db.query(ExperimentType).filter(ExperimentType.id == record.experiment_type_id).first()

            # 解析submission_data
            submission_data = None
            if record.submission_data:
                try:
                    import json
                    submission_data = json.loads(record.submission_data)
                except:
                    pass

            result.append(ExperimentRecordResponse(
                id=record.id,
                experiment_name=experiment_type.name if experiment_type else "未知实验",
                experiment_code=experiment_type.code if experiment_type else "unknown",
                submitted_at=record.submitted_at.isoformat(),
                status=record.status.value,
                is_passed=record.is_passed,
                score=float(record.score) if record.score else None,
                submission_data=submission_data,
                plot_data=record.plot_data,
                analysis_result=record.analysis_result
            ))

        return result

    except Exception as e:
        logger.error(f"获取学生实验记录失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取学生实验记录失败"
        )


@router.get("/experiments/current-control-circuit", response_model=List[CurrentControlCircuitResponse])
async def get_current_control_circuit_results(
    student_id: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """获取制流电路实验结果"""
    try:
        # 获取制流电路实验类型
        experiment_type = db.query(ExperimentType).filter(
            ExperimentType.code == "current_control_circuit"
        ).first()

        if not experiment_type:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="制流电路实验类型不存在"
            )

        # 查询实验记录
        query = db.query(ExperimentRecord).filter(
            ExperimentRecord.experiment_type_id == experiment_type.id
        )

        # 如果指定了学号，进行筛选
        if student_id:
            query = query.filter(
                ExperimentRecord.submission_data.like(f'%"student_id": "{student_id}"%')
            )

        records = query.order_by(ExperimentRecord.submitted_at.desc()).all()

        result = []
        for record in records:
            # 解析submission_data
            submission_data = {}
            if record.submission_data:
                try:
                    import json
                    submission_data = json.loads(record.submission_data)
                except:
                    pass

            # 解析plot_json - 从submission_data中获取
            plot_json_str = None
            if submission_data.get('plot_json'):
                plot_json_str = json.dumps(submission_data.get('plot_json'))
            elif submission_data.get('circuit_data'):
                # 如果有circuit_data字段，将其作为plot_json
                plot_json_str = json.dumps(submission_data.get('circuit_data'))

            # 提取实验数据
            voltage_data = submission_data.get('voltage_data', [])
            current_data = submission_data.get('current_data', [])
            resistance_values = submission_data.get('resistance_values', [])

            # 提取环境数据
            temperature = submission_data.get('temperature')
            humidity = submission_data.get('humidity')

            # 提取计算结果
            calculated_resistance = submission_data.get('calculated_resistance')
            theoretical_resistance = submission_data.get('theoretical_resistance')
            error_percentage = submission_data.get('error_percentage')

            # 提取分析结论
            conclusions = []
            if record.analysis_result:
                # 尝试从分析结果中提取结论
                analysis_lines = record.analysis_result.split('\n')
                for line in analysis_lines:
                    if '结论' in line or '总结' in line or '建议' in line:
                        conclusions.append(line.strip())

            result.append(CurrentControlCircuitResponse(
                id=record.id,
                student_name=submission_data.get('student_name', '未知'),
                student_id=submission_data.get('student_id', '未知'),
                submitted_at=record.submitted_at.isoformat(),
                is_passed=record.is_passed,
                score=float(record.score) if record.score else None,

                # 实验数据
                voltage_data=voltage_data,
                current_data=current_data,
                resistance_values=resistance_values,
                temperature=temperature,
                humidity=humidity,

                # 计算结果
                calculated_resistance=calculated_resistance,
                theoretical_resistance=theoretical_resistance,
                error_percentage=error_percentage,

                # 图形数据
                plot_data=record.plot_data,
                plot_json=plot_json_str,

                # 分析结果
                analysis_result=record.analysis_result,
                conclusions=conclusions
            ))

        # 确保返回正确编码的JSON响应
        return JSONResponse(
            content=result,
            headers={"Content-Type": "application/json; charset=utf-8"}
        )

    except HTTPException:
        raise
    except Exception as e:
        import traceback
        logger.error(f"获取制流电路实验结果失败: {e}")
        logger.error(f"错误详情: {traceback.format_exc()}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取制流电路实验结果失败: {str(e)}"
        )


@router.get("/experiments/current-control-circuit/{record_id}", response_model=CurrentControlCircuitResponse)
async def get_current_control_circuit_detail(
    record_id: int,
    db: Session = Depends(get_db)
):
    """获取单个制流电路实验结果详情"""
    try:
        record = db.query(ExperimentRecord).filter(ExperimentRecord.id == record_id).first()

        if not record:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="实验记录不存在"
            )

        # 验证是否为制流电路实验
        experiment_type = db.query(ExperimentType).filter(
            ExperimentType.id == record.experiment_type_id
        ).first()

        if not experiment_type or experiment_type.code != "current_control_circuit":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="该记录不是制流电路实验"
            )

        # 解析数据（与上面的逻辑相同）
        submission_data = {}
        if record.submission_data:
            try:
                import json
                submission_data = json.loads(record.submission_data)
            except:
                pass

        # 解析plot_json - 从submission_data中获取
        plot_json_str = None
        if submission_data.get('plot_json'):
            plot_json_str = json.dumps(submission_data.get('plot_json'))
        elif submission_data.get('circuit_data'):
            # 如果有circuit_data字段，将其作为plot_json
            plot_json_str = json.dumps(submission_data.get('circuit_data'))

        # 提取数据
        voltage_data = submission_data.get('voltage_data', [])
        current_data = submission_data.get('current_data', [])
        resistance_values = submission_data.get('resistance_values', [])
        temperature = submission_data.get('temperature')
        humidity = submission_data.get('humidity')
        calculated_resistance = submission_data.get('calculated_resistance')
        theoretical_resistance = submission_data.get('theoretical_resistance')
        error_percentage = submission_data.get('error_percentage')

        conclusions = []
        if record.analysis_result:
            analysis_lines = record.analysis_result.split('\n')
            for line in analysis_lines:
                if '结论' in line or '总结' in line or '建议' in line:
                    conclusions.append(line.strip())

        return CurrentControlCircuitResponse(
            id=record.id,
            student_name=submission_data.get('student_name', '未知'),
            student_id=submission_data.get('student_id', '未知'),
            submitted_at=record.submitted_at.isoformat(),
            is_passed=record.is_passed,
            score=float(record.score) if record.score else None,

            voltage_data=voltage_data,
            current_data=current_data,
            resistance_values=resistance_values,
            temperature=temperature,
            humidity=humidity,

            calculated_resistance=calculated_resistance,
            theoretical_resistance=theoretical_resistance,
            error_percentage=error_percentage,

            plot_data=record.plot_data,
            plot_json=plot_json_str,

            analysis_result=record.analysis_result,
            conclusions=conclusions
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取制流电路实验详情失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取制流电路实验详情失败"
        )


@router.get("/statistics/progress", response_model=LearningProgressStats)
async def get_learning_progress_stats(
    class_id: Optional[int] = None,
    group_id: Optional[int] = None,
    db: Session = Depends(get_db)
):
    """获取学习进度统计"""
    try:
        admin_service = AdminService(db)
        progress_stats = admin_service.get_learning_progress_stats(class_id=class_id, group_id=group_id)
        return progress_stats
    except Exception as e:
        logger.error(f"获取学习进度统计失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取学习进度统计失败"
        )


@router.get("/statistics/trends", response_model=List[SubmissionTrend])
async def get_submission_trends(
    days: int = 30,
    class_id: Optional[int] = None,
    db: Session = Depends(get_db)
):
    """获取提交趋势统计"""
    try:
        admin_service = AdminService(db)
        trends = admin_service.get_submission_trends(days=days, class_id=class_id)
        return trends
    except Exception as e:
        logger.error(f"获取提交趋势统计失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取提交趋势统计失败"
        )