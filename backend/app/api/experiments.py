"""
实验相关API路由
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
import logging

from app.utils.database import get_db
from app.utils.auth import get_current_user
from app.models.experiment import ExperimentType, ExperimentRecord
from app.services.experiment import ExperimentService
from app.services.plot import PlotService
from app.services.ai_analysis import AIAnalysisService

logger = logging.getLogger(__name__)

router = APIRouter()


class ExperimentTypeResponse(BaseModel):
    """实验类型响应模型"""
    id: int
    name: str
    code: str
    description: Optional[str]
    instructions: Optional[str]
    duration_minutes: int
    max_score: float
    is_active: bool


class ExperimentSubmissionRequest(BaseModel):
    """实验提交请求模型"""
    student_id: str
    name: str
    experiment_type_code: str
    submission_data: Dict[str, Any]


class ExperimentSubmissionResponse(BaseModel):
    """实验提交响应模型"""
    record_id: int
    message: str
    plot_data: Optional[str] = None
    analysis_result: Optional[str] = None
    is_passed: Optional[bool] = None


class PlotGenerationRequest(BaseModel):
    """图形生成请求模型"""
    experiment_code: str
    data: Dict[str, Any]


class PlotGenerationResponse(BaseModel):
    """图形生成响应模型"""
    plot_data: str
    message: str


class AnalysisRequest(BaseModel):
    """分析请求模型"""
    experiment_code: str
    data: Dict[str, Any]


class AnalysisResponse(BaseModel):
    """分析响应模型"""
    analysis_result: str
    is_passed: Optional[bool]
    message: str


@router.get("/types", response_model=List[ExperimentTypeResponse])
async def get_experiment_types(
    db: Session = Depends(get_db)
):
    """获取所有可用的实验类型"""
    try:
        experiment_service = ExperimentService(db)
        experiment_types = experiment_service.get_active_experiment_types()
        
        return [
            ExperimentTypeResponse(
                id=exp_type.id,
                name=exp_type.name,
                code=exp_type.code,
                description=exp_type.description,
                instructions=exp_type.instructions,
                duration_minutes=exp_type.duration_minutes,
                max_score=float(exp_type.max_score),
                is_active=exp_type.is_active
            )
            for exp_type in experiment_types
        ]
        
    except Exception as e:
        logger.error(f"获取实验类型失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取实验类型失败"
        )


@router.get("/types/{experiment_code}", response_model=ExperimentTypeResponse)
async def get_experiment_type(
    experiment_code: str,
    db: Session = Depends(get_db)
):
    """获取特定实验类型信息"""
    try:
        experiment_service = ExperimentService(db)
        experiment_type = experiment_service.get_experiment_type_by_code(experiment_code)
        
        if not experiment_type:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="实验类型不存在"
            )
        
        return ExperimentTypeResponse(
            id=experiment_type.id,
            name=experiment_type.name,
            code=experiment_type.code,
            description=experiment_type.description,
            instructions=experiment_type.instructions,
            duration_minutes=experiment_type.duration_minutes,
            max_score=float(experiment_type.max_score),
            is_active=experiment_type.is_active
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取实验类型失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取实验类型失败"
        )


@router.post("/submit", response_model=ExperimentSubmissionResponse)
async def submit_experiment(
    request: ExperimentSubmissionRequest,
    db: Session = Depends(get_db)
):
    """提交实验数据"""
    try:
        experiment_service = ExperimentService(db)

        # 使用请求中的学号和姓名创建或查找学生记录
        # 这里简化处理，直接使用学号作为标识
        result = experiment_service.submit_experiment_simple(
            student_id=request.student_id,
            student_name=request.name,
            experiment_code=request.experiment_type_code,
            submission_data=request.submission_data
        )

        record = result['record']
        is_unassigned = result['is_unassigned']

        # 构建响应消息
        message = "实验数据提交成功"
        if is_unassigned:
            message += "。注意：您的学号未在班级名单中，已添加到待分班学生列表，请联系老师进行班级分配。"

        return ExperimentSubmissionResponse(
            record_id=record.id,
            message=message,
            plot_data=result.get('plot_data'),
            analysis_result=result.get('analysis_result'),
            is_passed=result.get('is_passed', False)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"提交实验失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="提交实验失败"
        )


@router.get("/records")
async def get_experiment_records(
    experiment_code: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """获取实验记录"""
    try:
        experiment_service = ExperimentService(db)
        
        if current_user.get("user_type") == "student":
            # 学生只能查看自己的记录
            records = experiment_service.get_student_records(
                student_id=current_user["id"],
                experiment_code=experiment_code
            )
        else:
            # 管理员可以查看所有记录
            records = experiment_service.get_all_records(
                experiment_code=experiment_code
            )
        
        return {
            "records": [record.to_dict() for record in records],
            "total": len(records)
        }
        
    except Exception as e:
        logger.error(f"获取实验记录失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取实验记录失败"
        )


@router.post("/plot", response_model=PlotGenerationResponse)
async def generate_plot(
    request: PlotGenerationRequest,
    db: Session = Depends(get_db)
):
    """生成实验图形"""
    try:
        plot_service = PlotService()
        plot_data = plot_service.generate_plot(request.experiment_code, request.data)

        if not plot_data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无法生成图形，请检查数据格式"
            )

        # 兼容 PlotService 返回的多格式结果
        plot_data_str = plot_data.get('image_base64') if isinstance(plot_data, dict) else plot_data
        return PlotGenerationResponse(
            plot_data=plot_data_str,
            message="图形生成成功"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"生成图形失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="生成图形失败"
        )


@router.post("/analyze", response_model=AnalysisResponse)
async def analyze_experiment_data(
    request: AnalysisRequest,
    db: Session = Depends(get_db)
):
    """分析实验数据"""
    try:
        ai_service = AIAnalysisService()
        analysis_result, is_passed = ai_service.analyze_experiment(
            request.experiment_code,
            request.data
        )

        return AnalysisResponse(
            analysis_result=analysis_result,
            is_passed=is_passed,
            message="分析完成"
        )

    except Exception as e:
        logger.error(f"分析实验数据失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="分析实验数据失败"
        )
