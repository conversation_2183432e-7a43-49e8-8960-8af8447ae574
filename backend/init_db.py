#!/usr/bin/env python3
"""
数据库初始化脚本
"""

import asyncio
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from app.utils.config import settings
from app.models.base import Base
from app.models.user import Admin, Student, Class, Group, UnassignedStudent, UserRole
from app.models.experiment import ExperimentType, ExperimentRecord
from app.models.system import SystemConfig
from app.utils.auth import get_password_hash
from loguru import logger

def init_database():
    """初始化数据库"""
    try:
        # 创建数据库引擎
        engine = create_engine(settings.database_url)
        
        # 创建所有表
        logger.info("正在创建数据库表...")
        Base.metadata.create_all(bind=engine)
        logger.info("数据库表创建完成")
        
        # 插入初始数据
        logger.info("正在插入初始数据...")

        # 创建Session
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        db = SessionLocal()

        try:
            # 插入系统配置
            configs = [
                SystemConfig(config_key="system_name", config_value="大学物理实验基础指导平台", description="系统名称"),
                SystemConfig(config_key="version", config_value="1.0.0", description="系统版本"),
                SystemConfig(config_key="maintenance_mode", config_value="false", description="维护模式")
            ]
            for config in configs:
                existing = db.query(SystemConfig).filter(SystemConfig.config_key == config.config_key).first()
                if not existing:
                    db.add(config)

            # 插入班级数据
            classes = [
                Class(id=1, name="物理21-1班", code="PHY211", department="物理系", grade=2021),
                Class(id=2, name="物理21-2班", code="PHY212", department="物理系", grade=2021)
            ]
            for cls in classes:
                existing = db.query(Class).filter(Class.id == cls.id).first()
                if not existing:
                    db.add(cls)

            # 插入分组数据
            groups = [
                Group(id=1, name="第1组", class_id=1),
                Group(id=2, name="第2组", class_id=1),
                Group(id=3, name="第1组", class_id=2),
                Group(id=4, name="第2组", class_id=2)
            ]
            for group in groups:
                existing = db.query(Group).filter(Group.id == group.id).first()
                if not existing:
                    db.add(group)

            # 插入管理员
            admin_password = get_password_hash("admin123")
            admin = Admin(
                id=1,
                username="admin",
                name="系统管理员",
                password_hash=admin_password,
                role=UserRole.SUPER_ADMIN,
                email="<EMAIL>",
                is_active=True
            )
            existing_admin = db.query(Admin).filter(Admin.username == "admin").first()
            if not existing_admin:
                db.add(admin)

            # 插入学生数据
            student_password = get_password_hash("student123")
            students = [
                Student(id=1, student_id="2021001", name="张三", password_hash=student_password, class_id=1, group_id=1, email="<EMAIL>", is_active=True),
                Student(id=2, student_id="2021002", name="李四", password_hash=student_password, class_id=1, group_id=1, email="<EMAIL>", is_active=True),
                Student(id=3, student_id="2021003", name="王五", password_hash=student_password, class_id=1, group_id=2, email="<EMAIL>", is_active=True)
            ]
            for student in students:
                existing = db.query(Student).filter(Student.student_id == student.student_id).first()
                if not existing:
                    db.add(student)

            # 插入实验类型
            experiments = [
                ExperimentType(
                    id=1,
                    name="制流电路实验",
                    code="current_control_circuit",
                    description="通过测量不同k值下的电流变化，分析制流电路的特性",
                    instructions="本实验通过制流电路测量电流变化规律",
                    duration_minutes=60,
                    max_score=100,
                    is_active=True
                ),
                ExperimentType(
                    id=2,
                    name="示波器实验",
                    code="oscilloscope",
                    description="学习示波器的基本操作，观察和测量各种波形信号",
                    instructions="本实验学习示波器的使用方法",
                    duration_minutes=90,
                    max_score=100,
                    is_active=False
                )
            ]
            for exp in experiments:
                existing = db.query(ExperimentType).filter(ExperimentType.code == exp.code).first()
                if not existing:
                    db.add(exp)

            db.commit()
        except Exception as e:
            db.rollback()
            raise e
        finally:
            db.close()
            
        logger.info("初始数据插入完成")
        logger.info("数据库初始化成功！")
        
        # 显示默认账户信息
        print("\n" + "="*50)
        print("数据库初始化完成！")
        print("="*50)
        print("默认管理员账户:")
        print("  用户名: admin")
        print("  密码: admin123")
        print("\n默认学生账户:")
        print("  学号: 2021001, 密码: student123")
        print("  学号: 2021002, 密码: student123")
        print("  学号: 2021003, 密码: student123")
        print("="*50)
        
    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        raise

if __name__ == "__main__":
    init_database()
