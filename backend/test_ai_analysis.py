#!/usr/bin/env python3
"""
测试AI分析功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.ai_analysis import AIAnalysisService

def test_current_control_circuit_analysis():
    """测试制流电路实验AI分析"""
    print("开始测试制流电路实验AI分析...")
    
    # 创建AI分析服务实例
    ai_service = AIAnalysisService()
    
    # 测试数据 - 模拟一个正常的实验数据
    test_data = {
        'k1_current': [12.0, 10.9, 9.8, 8.7, 7.6, 6.5, 5.4, 4.3, 3.2, 2.1, 1.0],
        'k01_current': [12.0, 10.8, 9.6, 8.4, 7.2, 6.0, 4.8, 3.6, 2.4, 1.2, 0.6]
    }
    
    try:
        # 执行分析
        analysis_result, is_passed = ai_service.analyze_experiment(
            'current_control_circuit', 
            test_data
        )
        
        print("=" * 60)
        print("分析结果:")
        print("=" * 60)
        print(analysis_result)
        print("=" * 60)
        print(f"实验是否通过: {is_passed}")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        return False

def test_data_preprocessing():
    """测试数据预处理功能"""
    print("\n开始测试数据预处理...")
    
    ai_service = AIAnalysisService()
    
    # 测试数据
    k1_current = [12.0, 10.9, 9.8, 8.7, 7.6, 6.5, 5.4, 4.3, 3.2, 2.1, 1.0]
    k01_current = [12.0, 10.8, 9.6, 8.4, 7.2, 6.0, 4.8, 3.6, 2.4, 1.2, 0.6]
    
    try:
        processed_data = ai_service._preprocess_data_zhiliu(k1_current, k01_current)
        
        print("预处理结果:")
        print(f"理论k=1电流值: {processed_data['theoretical_k1']}")
        print(f"理论k=0.1电流值: {processed_data['theoretical_k01']}")
        print(f"k=1相对误差: {processed_data['relative_error_k1']}")
        print(f"k=0.1相对误差: {processed_data['relative_error_k01']}")
        
        return True
        
    except Exception as e:
        print(f"数据预处理测试失败: {e}")
        return False

if __name__ == "__main__":
    print("AI分析功能测试")
    print("=" * 60)
    
    # 测试数据预处理
    preprocessing_success = test_data_preprocessing()
    
    # 测试AI分析（需要AI服务运行）
    if preprocessing_success:
        print("\n注意：以下测试需要AI服务运行在 http://localhost:20001")
        print("如果AI服务未运行，测试将显示错误信息")
        analysis_success = test_current_control_circuit_analysis()
        
        if analysis_success:
            print("\n✅ 所有测试通过！")
        else:
            print("\n❌ AI分析测试失败")
    else:
        print("\n❌ 数据预处理测试失败")
