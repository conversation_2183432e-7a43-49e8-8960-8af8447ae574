# Docker 端口配置说明

本项目使用 30000 起始的端口段，避免与其他应用程序冲突。

## 端口映射

| 服务 | 容器内部端口 | 外部端口 | 环境变量 | 说明 |
|------|-------------|----------|----------|------|
| 数据库 (MySQL) | 3306 | ${DB_EXTERNAL_PORT} | DB_EXTERNAL_PORT=30001 | MySQL 数据库服务 |
| 后端 API | 8000 | ${API_EXTERNAL_PORT} | API_EXTERNAL_PORT=30000 | FastAPI 后端服务 |
| 前端应用 | 80 | ${FRONTEND_EXTERNAL_PORT} | FRONTEND_EXTERNAL_PORT=30002 | Vue.js 前端应用 |
| 管理后台 | 80 | ${ADMIN_EXTERNAL_PORT} | ADMIN_EXTERNAL_PORT=30003 | Vue.js 管理后台 |

## 访问地址

- **前端应用**: http://localhost:${FRONTEND_EXTERNAL_PORT}
- **管理后台**: http://localhost:${ADMIN_EXTERNAL_PORT}
- **API 文档**: http://localhost:${API_EXTERNAL_PORT}/docs
- **数据库**: localhost:${DB_EXTERNAL_PORT} (用户名: root, 密码: example)

**默认端口值**:
- FRONTEND_EXTERNAL_PORT=30002
- ADMIN_EXTERNAL_PORT=30003
- API_EXTERNAL_PORT=30000
- DB_EXTERNAL_PORT=30001

## 数据存储

- **数据库数据**: `/vdb_experiments` (主机目录)

## 环境变量配置

所有端口配置都通过根目录的 `.env` 文件进行管理：

```bash
# 端口配置 (30000 起始段)
DB_EXTERNAL_PORT=30001
API_EXTERNAL_PORT=30000
FRONTEND_EXTERNAL_PORT=30002
ADMIN_EXTERNAL_PORT=30003
```

修改端口时，只需要修改 `.env` 文件中的相应变量即可。

## 启动命令

```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 停止所有服务
docker-compose down

# 重新构建并启动
docker-compose up --build -d
```

## 测试服务状态

```bash
# 测试所有服务（使用环境变量）
curl -s -o /dev/null -w "%{http_code}" http://localhost:${API_EXTERNAL_PORT}/docs      # 后端 API
curl -s -o /dev/null -w "%{http_code}" http://localhost:${FRONTEND_EXTERNAL_PORT}     # 前端
curl -s -o /dev/null -w "%{http_code}" http://localhost:${ADMIN_EXTERNAL_PORT}        # 管理后台

# 或使用默认端口值
curl -s -o /dev/null -w "%{http_code}" http://localhost:30000/docs  # 后端 API
curl -s -o /dev/null -w "%{http_code}" http://localhost:30002       # 前端
curl -s -o /dev/null -w "%{http_code}" http://localhost:30003       # 管理后台
```

## 故障排除

如果某个服务无法访问，可以：

1. 检查服务状态：`docker-compose ps`
2. 查看服务日志：`docker-compose logs [service_name]`
3. 重启特定服务：`docker-compose restart [service_name]`
4. 完全重建：`docker-compose down && docker-compose up --build -d`
docker-compose down
```

## Docker网络架构说明

### 容器间通信
```
前端容器(nginx:80) → backend:8000 → 后端容器(FastAPI:8000)
管理后台容器(nginx:80) → backend:8000 → 后端容器(FastAPI:8000)
后端容器 → db:3306 → 数据库容器(MySQL:3306)
```

### 外部访问
```
用户浏览器 → localhost:${FRONTEND_EXTERNAL_PORT} → 前端容器:80
用户浏览器 → localhost:${ADMIN_EXTERNAL_PORT} → 管理后台容器:80
API客户端 → localhost:${API_EXTERNAL_PORT} → 后端容器:8000
数据库客户端 → localhost:${DB_EXTERNAL_PORT} → 数据库容器:3306
```

### 关键配置文件
- **docker-compose.yml**: 定义服务和端口映射
- **.env**: 定义所有端口环境变量
- **frontend/nginx.conf**: 前端nginx代理配置
- **admin/nginx.conf**: 管理后台nginx代理配置

## 注意事项

1. 确保主机上的 `/vdb_experiments` 目录存在且有适当的权限
2. 如果需要修改端口，只需修改 `.env` 文件中的相应环境变量
3. 前端和管理后台都已配置为通过 nginx 代理 API 请求到后端服务
4. **重要**: 容器内部端口（如8000、3306、80）不应随意修改，只修改外部映射端口
