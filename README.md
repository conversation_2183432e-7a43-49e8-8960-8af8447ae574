# 大学物理实验基础指导Web项目

## 项目概述

这是一个前后端分离的大学物理实验指导平台，支持多种物理实验的在线指导、数据收集、图形绘制和AI分析。

## 🆕 实验记录表系统

### 核心特性

- **多实验类型支持**: 制流电路实验、示波器实验、通用实验等
- **统一JSON格式**: 所有实验记录包含"实验数据"、"实验图形"、"实验分析"三个部分
- **智能解析机制**: 根据实验类型自动选择对应的解析器和显示组件
- **工厂模式设计**: 易于扩展新的实验类型
- **错误处理**: 解析失败时自动降级到通用格式
- **前端绘图**: 制流电路实验使用前端Plotly绘图，提升性能和交互性
- **AI智能分析**: 基于Gemini 2.5 Flash模型的实验结果智能分析

### 支持的实验类型

1. **制流电路实验** (`current_control_circuit`)
   - k=0.1和k=1的电流数据
   - 实验环境信息（温度、湿度）
   - 专门的数据表格展示
   - **前端Plotly绘图**: 图形在前端动态生成，提升性能和交互性

2. **示波器实验** (`oscilloscope`)
   - 频率、幅度、波形类型
   - 时间-电压测量数据
   - 波形图展示

3. **通用实验** (其他类型)
   - 支持任意格式的实验数据
   - 通用JSON数据展示
   - 错误信息提示

### 前端绘图系统

制流电路实验采用前端Plotly绘图，具有以下优势：

- **性能提升**: 减少服务器负载，图形生成更快
- **交互性强**: 支持缩放、平移、数据点悬停等交互功能
- **实时更新**: 数据变化时图表可实时更新
- **离线支持**: 无需网络连接即可生成图表
- **导出功能**: 支持PNG、SVG等格式导出

### AI智能分析系统

基于Gemini 2.5 Flash模型的实验结果智能分析，具有以下特点：

- **智能诊断**: 自动识别实验数据中的问题和误差
- **专业建议**: 提供具体的实验改进建议和错误纠正方法
- **准确判断**: 结合理论计算和AI分析，准确判断实验是否通过
- **详细报告**: 生成包含数据表格、问题分析和结论的详细报告

#### 分析能力

- **误差分析**: 计算理论值与实测值的相对误差
- **趋势检查**: 验证电流变化趋势是否符合理论预期
- **问题识别**: 识别常见实验错误（如起始点选择、电流调节等）
- **通过判断**: 基于±15%误差阈值和AI综合分析判断实验结果

#### 技术实现

- **PlotlyChart.vue**: 通用Plotly图表组件
- **plotlyUtils.ts**: 绘图工具函数库
- **动态导入**: 按需加载Plotly.js，减少初始包大小

### JSON格式示例

制流电路实验的JSON格式：
```json
{
  "实验数据": {
    "k=1电流数据": [
      { "接入比例": 0.1, "电流值(A)": 0.0012 }
    ],
    "k=0.1电流数据": [
      { "接入比例": 0.1, "电流值(A)": 0.00012 }
    ],
    "实验环境": {
      "温度(°C)": 25.5,
      "湿度(%)": 60
    }
  },
  "实验图形": {
    "生成方式": "前端Plotly生成",
    "图表类型": "制流电路实验图表",
    "说明": "图形在前端动态生成，不再存储图片数据"
  },
  "实验分析": "AI分析结果"
}
```

## 技术栈

### 前端
- Vue 3 + TypeScript
- Vite 构建工具
- Vue Router 路由管理
- Pinia 状态管理
- Element Plus UI组件库
- Plotly.js 前端绘图

### 后端
- FastAPI (Python)
- MySQL 数据库
- SQLAlchemy ORM
- Plotly 图形绘制
- JWT 认证

### 管理界面
- Vue 3 + TypeScript
- 独立的管理员界面

### AI分析系统
- Gemini 2.5 Flash模型
- 智能实验结果分析
- Markdown格式输出
- 自动误差计算
- 问题识别与建议

## 项目结构

```
physics-experiments/
├── frontend/              # Vue3+TS 学生端前端
│   ├── src/
│   │   ├── components/    # 通用组件
│   │   ├── views/         # 页面组件
│   │   ├── experiments/   # 实验相关组件
│   │   ├── api/           # API接口
│   │   ├── stores/        # Pinia状态管理
│   │   └── types/         # TypeScript类型定义
│   ├── public/
│   └── package.json
├── backend/               # FastAPI 后端
│   ├── app/
│   │   ├── api/           # API路由
│   │   ├── models/        # 数据模型
│   │   ├── services/      # 业务逻辑
│   │   ├── utils/         # 工具函数
│   │   └── main.py        # 应用入口
│   ├── requirements.txt
│   └── alembic/           # 数据库迁移
├── admin/                 # 管理员界面
│   ├── src/
│   └── package.json
├── database/              # 数据库相关
│   ├── init.sql           # 初始化脚本
│   └── migrations/        # 迁移脚本
└── docs/                  # 项目文档
```

## 支持的实验

### 1. 制流电路实验 - 已实现

#### 详细实验步骤
1. **器材准备与安全检查** (6个子步骤)
   - 晶体管直流稳压电源检查
   - 0.5级电流表准备
   - 电阻箱功能确认
   - 滑线变阻器检查
   - 钮子开关准备
   - 连接导线检查

2. **电路连接**
   - 详细连接说明和安全检查

3. **k=1 测量准备** (3个子步骤)
   - 电阻箱设置
   - 电源电压调节
   - 电流表量程确认

4. **k=1 数据测量**
   - 系统化数据采集

5. **k=0.1 测量准备** (7个子步骤)
   - 安全断电和参数重设

6. **k=0.1 数据测量**
   - 精确数据采集

7. **数据分析与图形生成**
   - 交互式图表显示
   - AI智能分析
   - Markdown格式结果
   - 实验步骤指导
   - 数据输入与验证
   - **前端Plotly图形绘制** - 提升性能和交互性
   - AI数据分析

2. **示波器实验** - 计划中
3. **其他物理实验** - 可扩展

## 功能特性

### 学生端功能
- 实验步骤指导
- 交互式数据输入
- 实时数据验证
- 图形可视化
- AI实验分析
- 实验结果提交

### 管理端功能
- 学生管理
- 班级和分组管理
- 实验监控
- 数据统计分析
- 实验结果审核

## 快速开始

### 环境要求
- Node.js 16+
- Python 3.8+
- MySQL 8.0+

### 安装步骤

1. 克隆项目
```bash
git clone <repository-url>
cd physics-experiments
```

2. 安装前端依赖
```bash
cd frontend
npm install
```

3. 安装后端依赖
```bash
cd ../backend
pip install -r requirements.txt
```

4. 配置数据库
```bash
cd ../database
mysql -u root -p < init.sql
```

5. 启动服务
```bash
# 启动后端
cd backend
uvicorn app.main:app --reload

# 启动前端
cd frontend
npm run dev
```

## 开发指南

详细的开发文档请参考 `docs/` 目录。

### 实验记录表系统文档

- [系统使用说明](docs/experiment-record-system.md) - 详细的使用指南和扩展方法
- [前端绘图迁移](docs/frontend-plotting-migration.md) - 前端Plotly绘图系统的实现和迁移过程
- [AI分析功能实现](docs/ai-analysis-implementation.md) - 基于Gemini 2.5 Flash的智能实验分析系统
- [示例数据](samples/experiment-record-examples.json) - 各种实验类型的JSON格式示例
- [测试工具](frontend/src/utils/experimentRecordTest.ts) - 用于验证系统功能的测试工具

### 核心组件

- `ExperimentRecordTableFactory.vue` - 工厂组件，根据实验类型路由到对应的记录表
- `CurrentControlCircuitRecordTable.vue` - 制流电路实验记录表
- `OscilloscopeRecordTable.vue` - 示波器实验记录表
- `GenericExperimentRecordTable.vue` - 通用实验记录表
- `experimentDataParser.ts` - 数据解析服务
- `PlotlyChart.vue` - 通用Plotly图表组件
- `plotlyUtils.ts` - 前端绘图工具函数

## 许可证

MIT License
