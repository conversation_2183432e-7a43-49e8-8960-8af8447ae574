#!/usr/bin/env python3
"""
测试修复的脚本
"""
import requests
import json
import sys

def test_api_endpoint():
    """测试API端点是否正常工作"""
    try:
        url = "http://localhost:8000/api/admin/experiments/current-control-circuit"
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API测试成功: 返回了 {len(data)} 条记录")
            
            # 检查数据结构
            if data:
                first_record = data[0]
                required_fields = ['id', 'student_name', 'student_id', 'submitted_at']
                missing_fields = [field for field in required_fields if field not in first_record]
                
                if missing_fields:
                    print(f"⚠️  缺少字段: {missing_fields}")
                else:
                    print("✅ 数据结构正确")
            
            return True
        else:
            print(f"❌ API测试失败: HTTP {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ API请求失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        return False

def test_json_encoding():
    """测试JSON编码问题是否修复"""
    try:
        # 模拟包含中文的数据
        test_data = {
            "experiment_data": {
                "k1_current_data": [{"ratio": 1.0, "current": 0.001}],
                "k01_current_data": [{"ratio": 0.1, "current": 0.0001}],
                "environment": {
                    "temperature": 25.0,
                    "humidity": 60.0
                },
                "notes": "测试备注"
            }
        }
        
        # 测试JSON序列化
        json_str = json.dumps(test_data, ensure_ascii=False)
        parsed_data = json.loads(json_str)
        
        if parsed_data == test_data:
            print("✅ JSON编码测试成功")
            return True
        else:
            print("❌ JSON编码测试失败: 数据不匹配")
            return False
            
    except Exception as e:
        print(f"❌ JSON编码测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试修复...")
    print("=" * 50)
    
    tests = [
        ("API端点测试", test_api_endpoint),
        ("JSON编码测试", test_json_encoding),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}:")
        if test_func():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！修复成功！")
        return 0
    else:
        print("❌ 部分测试失败，需要进一步检查")
        return 1

if __name__ == "__main__":
    sys.exit(main())
