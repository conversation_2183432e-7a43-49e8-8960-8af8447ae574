<!DOCTYPE html>
<html>
<head>
    <title>Frontend API Test</title>
</head>
<body>
    <h1>Frontend API Test</h1>
    <button onclick="testAPI()">Test API Connection</button>
    <div id="result"></div>

    <script>
        // This should match the frontend's API_BASE
        const API_BASE = 'http://localhost:8000';
        
        async function testAPI() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<p>Testing...</p>';
            
            try {
                // Test plot API
                const plotResponse = await fetch(`${API_BASE}/api/experiments/plot`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        experiment_code: 'current_control_circuit',
                        data: {
                            k1Data: [{"ratio": 0.1, "current": 2}, {"ratio": 0.2, "current": 5}],
                            k01Data: [{"ratio": 0.1, "current": 1}, {"ratio": 0.2, "current": 3}]
                        }
                    })
                });
                
                const plotResult = await plotResponse.json();
                
                // Test analyze API
                const analyzeResponse = await fetch(`${API_BASE}/api/experiments/analyze`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        experiment_code: 'current_control_circuit',
                        data: {
                            k1Data: [{"ratio": 0.1, "current": 2}, {"ratio": 0.2, "current": 5}],
                            k01Data: [{"ratio": 0.1, "current": 1}, {"ratio": 0.2, "current": 3}]
                        }
                    })
                });
                
                const analyzeResult = await analyzeResponse.json();
                
                resultDiv.innerHTML = `
                    <h3>✅ API Test Successful!</h3>
                    <h4>Plot API:</h4>
                    <p>Status: ${plotResponse.status}</p>
                    <p>Message: ${plotResult.message}</p>
                    <p>Has plot_data: ${!!plotResult.plot_data}</p>
                    
                    <h4>Analyze API:</h4>
                    <p>Status: ${analyzeResponse.status}</p>
                    <p>Message: ${analyzeResult.message}</p>
                    <p>Analysis: ${analyzeResult.analysis_result}</p>
                    <p>Passed: ${analyzeResult.is_passed}</p>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <h3>❌ API Test Failed!</h3>
                    <p>Error: ${error.message}</p>
                    <p>This suggests CORS or network issues.</p>
                `;
                console.error('API test error:', error);
            }
        }
    </script>
</body>
</html>
