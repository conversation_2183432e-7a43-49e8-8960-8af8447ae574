<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API连接测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>API连接测试</h1>
    
    <div class="test-section">
        <h2>基础连接测试</h2>
        <button onclick="testBasicConnection()">测试基础连接</button>
        <div id="basic-result"></div>
    </div>
    
    <div class="test-section">
        <h2>AI分析功能测试</h2>
        <button onclick="testAIAnalysis()">测试AI分析</button>
        <div id="ai-result"></div>
    </div>

    <script>
        async function testBasicConnection() {
            const resultDiv = document.getElementById('basic-result');
            resultDiv.innerHTML = '<p>测试中...</p>';
            
            try {
                const response = await fetch('/health');
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h3>✅ 基础连接成功</h3>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h3>❌ 基础连接失败</h3>
                        <p>错误: ${error.message}</p>
                    </div>
                `;
            }
        }

        async function testAIAnalysis() {
            const resultDiv = document.getElementById('ai-result');
            resultDiv.innerHTML = '<p>测试中...</p>';
            
            const testData = {
                experiment_code: "current_control_circuit",
                data: {
                    k1_current: [12.0, 10.9, 9.8, 8.7, 7.6, 6.5, 5.4, 4.3, 3.2, 2.1, 1.0],
                    k01_current: [12.0, 10.8, 9.6, 8.4, 7.2, 6.0, 4.8, 3.6, 2.4, 1.2, 0.6]
                }
            };
            
            try {
                const response = await fetch('/api/experiments/analyze', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testData)
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h3>✅ AI分析功能正常</h3>
                            <p><strong>分析结果:</strong> ${data.is_passed ? '通过' : '未通过'}</p>
                            <details>
                                <summary>查看详细分析结果</summary>
                                <div style="margin-top: 10px;">${data.analysis_result}</div>
                            </details>
                        </div>
                    `;
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h3>❌ AI分析功能失败</h3>
                        <p>错误: ${error.message}</p>
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
