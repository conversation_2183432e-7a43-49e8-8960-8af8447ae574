<template>
  <div class="experiment-page">
    <!-- 实验标题 -->
    <header class="experiment-header">
      <h1>制流电路实验</h1>
      <p>通过测量不同接入比例下的电流变化，掌握制流电路的工作原理和特性</p>
    </header>

    <!-- 实验内容 -->
    <!-- 统一导航：步骤指示器 -->
    <StepIndicator :steps="steps" :current-step="currentStep" :current-substep="currentSubstep" />

    <main class="experiment-content">
      <!-- 步骤1: 器材准备与安全检查 -->
      <section class="step-section" v-if="currentStep === 1">
        <h2>步骤1: {{ steps[0].title }}</h2>
        <div class="step-description">
          <p>{{ steps[0].description }}</p>
        </div>

        <!-- 子步骤内容 -->
        <div class="substep-content" v-if="steps[0].substeps">
          <div class="substep-item" v-for="(substep, index) in steps[0].substeps" :key="index"
               v-show="currentSubstep === index + 1">
            <div class="substep-header">
              <div class="substep-number">{{ index + 1 }}</div>
              <h3 class="substep-title">{{ substep.title }}</h3>
            </div>

            <div class="substep-body">
              <p class="substep-content-text">{{ substep.content }}</p>

              <div class="checklist" v-if="substep.checklist">
                <h4>检查项目：</h4>
                <ul>
                  <li v-for="item in substep.checklist" :key="item">{{ item }}</li>
                </ul>
              </div>

              <div class="instruction" v-if="substep.instruction">
                <h4>提示：</h4>
                <p>{{ substep.instruction }}</p>
              </div>

              <div class="warning" v-if="substep.warning">
                <h4>注意事项：</h4>
                <p>{{ substep.warning }}</p>
              </div>
            </div>
          </div>
        </div>

        <ExperimentNavigation
          :current-step="currentStep"
          :total-steps="totalSteps"
          :current-substep="currentSubstep"
          :total-substeps="totalSubsteps"
          @prev="goPrev"
          @next="goNext"
          :show-prev="currentSubstep > 1"
        />
      </section>

      <!-- 步骤2: 电路连接 -->
      <section class="step-section" v-if="currentStep === 2">
        <h2>步骤2: {{ steps[1].title }}</h2>
        <div class="step-description">
          <p>{{ steps[1].description }}</p>
        </div>

        <div class="circuit-diagram">
          <h3>电路连接图</h3>
          <div class="diagram-placeholder">
            <p>制流电路连接示意图</p>
            <p>（此处应显示电路图）</p>
          </div>
        </div>

        <div class="step-content">
          <p class="substep-content-text">{{ steps[1].content }}</p>

          <div class="checklist">
            <h4>安全检查项目：</h4>
            <ul>
              <li v-for="item in steps[1].checklist" :key="item">{{ item }}</li>
            </ul>
          </div>

          <div class="instruction">
            <h4>提示：</h4>
            <p>{{ steps[1].instruction }}</p>
          </div>

          <div class="warning">
            <h4>注意事项：</h4>
            <p>{{ steps[1].warning }}</p>
          </div>

          <div class="success-message">
            <p>✅ 电路连接已完成并检查无误！您可以继续下一步实验。</p>
          </div>
        </div>
        <ExperimentNavigation
          :current-step="currentStep"
          :total-steps="totalSteps"
          :current-substep="currentSubstep"
          :total-substeps="totalSubsteps"
          :has-validation="true"
          :validated="k1Valid"
          @prev="goPrev"
          @next="goNext"
        />
      </section>

      <!-- 步骤3: k=1 测量准备 -->
      <section class="step-section" v-if="currentStep === 3">
        <h2>步骤3: {{ steps[2].title }}</h2>
        <div class="step-description">
          <p>{{ steps[2].description }}</p>
        </div>

        <!-- 子步骤内容 -->
        <div class="substep-content" v-if="steps[2].substeps">
          <div class="substep-item" v-for="(substep, index) in steps[2].substeps" :key="index"
               v-show="currentSubstep === index + 1">
            <div class="substep-header">
              <div class="substep-number">{{ index + 1 }}</div>
              <h3 class="substep-title">{{ substep.title }}</h3>
            </div>

            <div class="substep-body">
              <p class="substep-content-text">{{ substep.content }}</p>

              <div class="checklist" v-if="substep.checklist">
                <h4>操作步骤：</h4>
                <ul>
                  <li v-for="item in substep.checklist" :key="item">{{ item }}</li>
                </ul>
              </div>

              <div class="instruction" v-if="substep.instruction">
                <h4>提示：</h4>
                <p>{{ substep.instruction }}</p>
              </div>

              <div class="warning" v-if="substep.warning">
                <h4>注意事项：</h4>
                <p>{{ substep.warning }}</p>
              </div>
            </div>
          </div>
        </div>
        <ExperimentNavigation
          :current-step="currentStep"
          :total-steps="totalSteps"
          :current-substep="currentSubstep"
          :total-substeps="totalSubsteps"
          @prev="goPrev"
          @next="goNext"
          :show-prev="currentSubstep > 1"
        />
      </section>

      <!-- 步骤4: k=1 数据测量 -->
      <section class="step-section" v-if="currentStep === 4">
        <h2>步骤4: {{ steps[3].title }}</h2>
        <div class="step-description">
          <p>{{ steps[3].description }}</p>
        </div>

        <div class="step-content">
          <p class="substep-content-text">{{ steps[3].content }}</p>

          <div class="instruction">
            <h4>提示：</h4>
            <p>{{ steps[3].instruction }}</p>
          </div>

          <div class="warning">
            <h4>注意事项：</h4>
            <p>{{ steps[3].warning }}</p>
          </div>

          <div class="data-table">
            <h3>k=1 测量数据</h3>
            <table>
              <thead>
                <tr>
                  <th>接入比例</th>
                  <th>电流值 (mA)</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="(item, index) in k1Data" :key="index">
                  <td>{{ item.ratio }}</td>
                  <td>
                    <el-input
                      v-model.number="item.current"
                      type="number"
                      placeholder="请输入电流值"
                      :min="0"
                      :max="50"
                      size="small"
                    />
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <ExperimentNavigation
          :current-step="currentStep"
          :total-steps="totalSteps"
          :current-substep="currentSubstep"
          :total-substeps="totalSubsteps"
          :has-validation="true"
          :validated="k1Valid"
          @prev="goPrev"
          @next="goNext"
        />
      </section>

      <!-- 步骤5: k=0.1 测量准备 -->
      <section class="step-section" v-if="currentStep === 5">
        <h2>步骤5: {{ steps[4].title }}</h2>
        <div class="step-description">
          <p>{{ steps[4].description }}</p>
        </div>

        <!-- 子步骤内容 -->
        <div class="substep-content" v-if="steps[4].substeps">
          <div class="substep-item" v-for="(substep, index) in steps[4].substeps" :key="index"
               v-show="currentSubstep === index + 1">
            <div class="substep-header">
              <div class="substep-number">{{ index + 1 }}</div>
              <h3 class="substep-title">{{ substep.title }}</h3>
            </div>

            <div class="substep-body">
              <p class="substep-content-text">{{ substep.content }}</p>

              <div class="checklist" v-if="substep.checklist">
                <h4>操作步骤：</h4>
                <ul>
                  <li v-for="item in substep.checklist" :key="item">{{ item }}</li>
                </ul>
              </div>

              <div class="instruction" v-if="substep.instruction">
                <h4>提示：</h4>
                <p>{{ substep.instruction }}</p>
              </div>

              <div class="warning" v-if="substep.warning">
                <h4>注意事项：</h4>
                <p>{{ substep.warning }}</p>
              </div>
            </div>
          </div>
        </div>
        <ExperimentNavigation
          :current-step="currentStep"
          :total-steps="totalSteps"
          :current-substep="currentSubstep"
          :total-substeps="totalSubsteps"
          @prev="goPrev"
          @next="goNext"
          :show-prev="currentSubstep > 1"
        />
      </section>

      <!-- 步骤6: k=0.1 数据测量 -->
      <section class="step-section" v-if="currentStep === 6">
        <h2>步骤6: {{ steps[5].title }}</h2>
        <div class="step-description">
          <p>{{ steps[5].description }}</p>
        </div>

        <div class="step-content">
          <p class="substep-content-text">{{ steps[5].content }}</p>

          <div class="instruction">
            <h4>提示：</h4>
            <p>{{ steps[5].instruction }}</p>
          </div>

          <div class="warning">
            <h4>注意事项：</h4>
            <p>{{ steps[5].warning }}</p>
          </div>

          <div class="data-table">
            <h3>k=0.1 测量数据</h3>
            <table>
              <thead>
                <tr>
                  <th>接入比例</th>
                  <th>电流值 (mA)</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="(item, index) in k01Data" :key="index">
                  <td>{{ item.ratio }}</td>
                  <td>
                    <el-input
                      v-model.number="item.current"
                      type="number"
                      placeholder="请输入电流值"
                      :min="0"
                      :max="50"
                      size="small"
                    />
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <ExperimentNavigation
          :current-step="currentStep"
          :total-steps="totalSteps"
          :current-substep="currentSubstep"
          :total-substeps="totalSubsteps"
          :has-validation="true"
          :validated="k01Valid"
          @prev="goPrev"
          @next="goNext"
        />
      </section>

      <!-- 步骤7: 数据分析 -->
      <section class="step-section" v-if="currentStep === 7">
        <h2>步骤7: {{ steps[6].title }}</h2>
        <div class="step-description">
          <p>{{ steps[6].description }}</p>
        </div>

        <div class="step-content">
          <p class="substep-content-text">{{ steps[6].content }}</p>

          <div class="instruction">
            <h4>提示：</h4>
            <p>{{ steps[6].instruction }}</p>
          </div>

          <div class="warning">
            <h4>注意事项：</h4>
            <p>{{ steps[6].warning }}</p>
          </div>

          <!-- 图表显示 -->
          <div class="chart-container" v-if="showPlot">
            <h3>电流-接入比例关系图</h3>
            <PlotlyChart
              ref="plotlyChart"
              :data="plotData.data"
              :layout="plotData.layout"
              :config="plotData.config"
              :width="800"
              :height="600"
              @plot-ready="onPlotReady"
              @plot-error="onPlotError"
            />
          </div>

          <!-- AI分析结果 -->
          <div class="analysis-result" v-if="analysisResult">
            <h3>AI智能分析结果</h3>
            <div class="analysis-content">
              <div class="analysis-status">
                <p><strong>实验状态：</strong>
                  <el-tag :type="isPassed ? 'success' : 'warning'">
                    {{ isPassed ? '✅ 实验通过' : '⚠️ 需要改进' }}
                  </el-tag>
                </p>
              </div>
              <div class="analysis-details" v-html="renderedAnalysisResult"></div>
            </div>
          </div>

          <div class="analysis-actions">
            <el-button type="primary" @click="generatePlot" :loading="plotting">
              {{ plotting ? '绘图中...' : '生成图形' }}
            </el-button>
            <el-button type="success" @click="generateAnalysis" :loading="analyzing">
              {{ analyzing ? '分析中...' : '生成分析' }}
            </el-button>
          </div>
        </div>
        <ExperimentNavigation
          :current-step="currentStep"
          :total-steps="totalSteps"
          :current-substep="currentSubstep"
          :total-substeps="totalSubsteps"
          :has-validation="true"
          :validated="Boolean(analysisResult)"
          @prev="goPrev"
          @next="goNext"
        />
      </section>

      <!-- 步骤5: 提交实验 -->
      <section class="step-section" v-if="currentStep === 5">
        <h2>步骤5: 提交实验报告</h2>
        <div class="step-content">
          <div class="submit-form">
            <h3>学生信息</h3>
            <el-form :model="studentInfo" label-width="100px">
              <el-form-item label="学号">
                <el-input v-model="studentInfo.studentId" placeholder="请输入学号" />
              </el-form-item>
              <el-form-item label="姓名">
                <el-input v-model="studentInfo.name" placeholder="请输入姓名" />
              </el-form-item>
            </el-form>
          </div>

          <div class="experiment-summary">
            <h3>实验总结</h3>
            <p><strong>实验状态：</strong>
              <el-tag :type="isPassed ? 'success' : 'warning'">
                {{ isPassed ? '实验通过' : '需要改进' }}
              </el-tag>
            </p>
            <p><strong>完成时间：</strong>{{ new Date().toLocaleString() }}</p>
          </div>
        </div>
        <ExperimentNavigation
          :current-step="currentStep"
          :total-steps="totalSteps"
          :current-substep="currentSubstep"
          :total-substeps="totalSubsteps"
          :show-next="true"
          :show-prev="true"
          @prev="goPrev"
          @next="submitExperiment"
        />
      </section>
    </main>

    <!-- 步骤指示器 -->
    <footer class="step-indicator">
      <div class="steps">
        <div
          v-for="step in 5"
          :key="step"
          :class="['step-item', { active: currentStep === step, completed: currentStep > step }]"
        >
          {{ step }}
        </div>
      </div>
      <p>步骤 {{ currentStep }} / 5</p>
    </footer>

    <!-- 自动保存指示器 -->
    <AutoSaveIndicator
      :enabled="autoSaveEnabled"
      :experiment-type="EXPERIMENT_TYPE"
      ref="autoSaveIndicator"
    />

    <!-- 数据恢复对话框 -->
    <DataRecoveryDialog
      v-model="showRecoveryDialog"
      :cache-data="recoveryData"
      :cache-timestamp="recoveryTimestamp"
      @restore="handleDataRestore"
      @start-new="handleStartNew"
    />

    <!-- 步骤恢复对话框 -->
    <StepRecoveryDialog
      v-model="stepRecovery.showRecoveryDialog.value"
      :current-step="currentStep"
      :current-substep="currentSubstep"
      :step-title="steps[currentStep - 1]?.title || ''"
      :problem-description="getStepProblemDescription()"
      :debug-data="stepRecovery.debugInfo.value"
      @reset-step="handleResetStep"
      @skip-validation="handleSkipValidation"
      @go-previous="goPrev"
      @show-help="handleShowHelp"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'
import { useStepRecovery } from '@/composables/useStepRecovery'

const router = useRouter()
import StepIndicator from '@/components/StepIndicator.vue'
import ExperimentNavigation from '@/components/ExperimentNavigation.vue'
import AutoSaveIndicator from '@/components/AutoSaveIndicator.vue'
import DataRecoveryDialog from '@/components/DataRecoveryDialog.vue'
import StepRecoveryDialog from '@/components/StepRecoveryDialog.vue'
import PlotlyChart from '@/components/charts/PlotlyChart.vue'
import cacheService from '@/services/cacheService'
import { generateCurrentControlCircuitPlot } from '@/utils/plotlyUtils'
import { experimentsApi } from '@/api/experiments'
import { marked } from 'marked'

// Unified API base from Vite env (injected via vite.config or .env.development)
const API_BASE = (import.meta.env.VITE_API_BASE as string) || 'http://localhost:8000'

// 详细的步骤与子步骤定义
const steps = [
  {
    title: '器材准备与安全检查',
    description: '确认所有实验器材完好可用，并进行必要的安全检查',
    substeps: [
      {
        title: '准备晶体管直流稳压电源',
        content: '请确认晶体管直流稳压电源是否可用，并检查以下事项：',
        checklist: [
          '输出接线柱是否稳固',
          '调压旋钮和调流旋钮是否灵活可控'
        ],
        instruction: '晶体管直流稳压电源将用于为制流电路提供稳定的电压。',
        warning: ''
      },
      {
        title: '准备0.5级电流表',
        content: '请检查电流表是否可用，并确认以下事项：',
        checklist: [
          '表针是否位于零位',
          '接线柱是否稳固',
          '量程选择旋钮是否正常'
        ],
        instruction: '电流表将用于测量电路中的电流值，精度为0.5级。',
        warning: '电流表应先设置在大量程，以防止初次通电时可能的大电流损坏电流表。'
      },
      {
        title: '准备电阻箱',
        content: '请确认电阻箱是否可用，并检查以下事项：',
        checklist: [
          '旋钮是否灵活可调',
          '接线柱是否牢固'
        ],
        instruction: '电阻箱将用于设置电路中的Rz值，它与滑线变阻器的总阻值R0的比值决定了k值(k=Rz/R0)。',
        warning: '电阻箱的某个电阻烧毁外观无法判断，但相关挡位的电阻值会下降到1/10左右。'
      },
      {
        title: '准备滑线变阻器',
        content: '请确认滑线变阻器是否可用，并检查以下事项：',
        checklist: [
          '滑动接触是否平滑',
          '滑动臂是否牢固',
          '接线柱是否完好'
        ],
        instruction: '滑线变阻器的总阻值即R0，接入电阻RA和调整接入比例。接入比例是接入电阻RA与总阻值R0的比值，也等于绕线部分接入长度与总长度的比值。',
        warning: ''
      },
      {
        title: '准备钮子开关',
        content: '请确认钮子开关是否可用，并检查以下事项：',
        checklist: [
          '接线柱是否牢固',
          '开关动作是否灵活'
        ],
        instruction: '钮子开关将用于控制电路的通断，确保安全操作。',
        warning: ''
      },
      {
        title: '准备连接导线',
        content: '请确认连接导线是否齐全，并检查以下事项：',
        checklist: [
          '导线接头是否清洁无氧化',
          '导线长短合适、线数量足够'
        ],
        instruction: '导线用于连接各个器材，确保电路连接可靠。',
        warning: ''
      }
    ]
  },
  {
    title: '电路连接',
    description: '按照电路图正确连接各个器材，确保连接牢固可靠',
    content: '请按照电路图连接制流电路，注意以下安全事项：',
    checklist: [
      '电源处于关闭状态',
      '注意电流表正负极连接',
      '开关处于断开状态',
      '滑线变阻器接触点置中部',
      '导线排布整齐，无交叉短路风险'
    ],
    instruction: '制流电路是一种能够在负载变化时保持电流基本恒定的电路。',
    warning: '连接前务必确保电源关闭，避免短路和触电危险。'
  },
  {
    title: 'k=1 测量准备',
    description: '设置电阻箱使k=1，并进行测量前的准备工作',
    substeps: [
      {
        title: '设置电阻箱 k=1',
        content: '调节电阻箱，使Rz=R0，即k=Rz/R0=1：',
        checklist: [
          '查看滑线变阻器标称阻值R0',
          '将电阻箱调节至相同阻值',
          '确认电阻箱读数正确'
        ],
        instruction: 'k值是电阻箱阻值与滑线变阻器总阻值的比值，k=1时两者相等。',
        warning: '确保电阻箱设置准确，这直接影响实验结果。'
      },
      {
        title: '设置电源电压为5V',
        content: '调节稳压电源输出电压：',
        checklist: [
          '打开电源开关',
          '调节电压旋钮至5V',
          '确认电压表读数为5.0V'
        ],
        instruction: 'k=1时使用5V电压，便于后续与k=0.1时的电流对比。',
        warning: '调节电压时动作要缓慢，避免电压突变损坏器材。'
      },
      {
        title: '电流表量程确认',
        content: '设置电流表合适的量程：',
        checklist: [
          '估算最大可能电流值',
          '选择合适的量程档位',
          '确认表针在零位'
        ],
        instruction: '选择量程时要确保能够准确读数，同时避免超量程。',
        warning: '初次测量建议使用较大量程，确认电流范围后再调整。'
      }
    ]
  },
  {
    title: 'k=1 数据测量',
    description: '测量k=1时不同接入比例下的电流值',
    content: '按照接入比例从0.0到1.0，每次增加0.1进行测量：',
    instruction: '每次调节滑线变阻器时要缓慢进行，等待电流稳定后再读数。',
    warning: '注意观察电流表指针，如有异常立即断开电路。'
  },
  {
    title: 'k=0.1 测量准备',
    description: '设置电阻箱使k=0.1，并调节电压使初始电流与k=1时相等',
    substeps: [
      {
        title: '断开电路',
        content: '安全地断开电路连接：',
        checklist: [
          '断开开关',
          '关闭电源',
          '等待电路完全断电'
        ],
        instruction: '更换电阻箱设置前必须断开电路，确保安全。',
        warning: '断电后等待几秒钟再进行操作，避免残余电荷。'
      },
      {
        title: '电压调零',
        content: '将电源电压调节至零：',
        checklist: [
          '将电压调节旋钮逆时针转到底',
          '确认电压表显示为0V'
        ],
        instruction: '调零是为了安全地重新设置电路参数。',
        warning: ''
      },
      {
        title: '设置电阻箱 k=0.1',
        content: '调节电阻箱，使k=Rz/R0=0.1：',
        checklist: [
          '计算所需电阻值 Rz=0.1×R0',
          '将电阻箱调节至计算值',
          '确认电阻箱读数正确'
        ],
        instruction: 'k=0.1时电阻箱阻值应为滑线变阻器总阻值的1/10。',
        warning: '仔细核对电阻箱设置，错误的k值会导致实验失败。'
      },
      {
        title: '设置滑线变阻器初始位置',
        content: '将滑线变阻器调至接入比例为0的位置：',
        checklist: [
          '将滑动触头移至起始端',
          '确认接入比例为0',
          '检查接触良好'
        ],
        instruction: '初始位置应使接入电阻为零，即滑动触头在起始端。',
        warning: '确保滑动触头与电阻丝接触良好。'
      },
      {
        title: '闭合开关',
        content: '重新闭合电路开关：',
        checklist: [
          '确认所有连接正确',
          '闭合开关',
          '观察电路状态'
        ],
        instruction: '闭合开关前再次检查电路连接。',
        warning: '如发现异常立即断开开关。'
      },
      {
        title: '调节电压使初始电流相等',
        content: '调节电源电压，使接入比例为0时的电流与k=1时相等：',
        checklist: [
          '缓慢增加电源电压',
          '观察电流表读数',
          '调节至与k=1时初始电流相等'
        ],
        instruction: '这样设置是为了比较k=1和k=0.1时的调节范围和精度。',
        warning: '调节过程中密切观察电流表，避免过流。'
      },
      {
        title: '记录初始电流',
        content: '记录调节后的初始电流值：',
        checklist: [
          '确认电流稳定',
          '准确读取电流表数值',
          '记录在数据表中'
        ],
        instruction: '初始电流应与k=1时的初始电流基本相等。',
        warning: '如果差异较大，需要重新调节电压。'
      }
    ]
  },
  {
    title: 'k=0.1 数据测量',
    description: '测量k=0.1时不同接入比例下的电流值',
    content: '按照接入比例从0.0到1.0，每次增加0.1进行测量：',
    instruction: '测量方法与k=1时相同，注意观察电流变化规律。',
    warning: 'k=0.1时电流变化更加敏感，调节时要更加小心。'
  },
  {
    title: '数据分析与图形生成',
    description: '对测量数据进行分析，生成图表并进行AI智能分析',
    content: '使用测量数据生成电流-接入比例关系图，并进行智能分析：',
    instruction: '通过图表可以直观地观察制流电路的特性，AI分析将帮助判断实验结果的正确性。',
    warning: '如果分析结果显示实验有问题，请根据建议重新进行测量。'
  }
]

const totalSteps = steps.length
const currentSubstep = ref(1)

const totalSubsteps = computed(() => {
  const sub = steps[currentStep.value - 1]?.substeps
  return Array.isArray(sub) ? sub.length : 0
})

// 用于表格验证
const k1Valid = computed(() => k1Data.value.every(r => r.current !== null && !Number.isNaN(r.current)))
const k01Valid = computed(() => k01Data.value.every(r => r.current !== null && !Number.isNaN(r.current)))

// Markdown渲染
const renderedAnalysisResult = computed(() => {
  if (!analysisResult.value) return ''
  try {
    return marked(analysisResult.value)
  } catch (error) {
    console.error('Markdown渲染失败:', error)
    return analysisResult.value
  }
})

// 缓存相关
const EXPERIMENT_TYPE = 'current_control_circuit'
const autoSaveEnabled = ref(true)
const autoSaveIndicator = ref()

// 数据恢复相关
const showRecoveryDialog = ref(false)
const recoveryData = ref(null)
const recoveryTimestamp = ref('')

// 获取当前实验数据
const getCurrentExperimentData = () => {
  return {
    currentStep: currentStep.value,
    currentSubstep: currentSubstep.value,
    k1Data: k1Data.value,
    k01Data: k01Data.value,
    studentInfo: studentInfo.value,
    plotImageUrl: plotImageUrl.value,
    analysisResult: analysisResult.value,
    isPassed: isPassed.value
  }
}

// 恢复实验数据
const restoreExperimentData = (data: any) => {
  if (data.currentStep) currentStep.value = data.currentStep
  if (data.currentSubstep) currentSubstep.value = data.currentSubstep
  if (data.k1Data) k1Data.value = data.k1Data
  if (data.k01Data) k01Data.value = data.k01Data
  if (data.studentInfo) studentInfo.value = data.studentInfo
  if (data.plotImageUrl) plotImageUrl.value = data.plotImageUrl
  if (data.analysisResult) analysisResult.value = data.analysisResult
  if (data.isPassed !== undefined) isPassed.value = data.isPassed
}

// 手动保存数据
const saveExperimentData = async () => {
  if (!autoSaveEnabled.value) return

  try {
    const currentData = getCurrentExperimentData()
    await cacheService.manualSave(EXPERIMENT_TYPE, currentData)
  } catch (error) {
    console.error('保存实验数据失败:', error)
  }
}

// 导航
const goPrev = async () => {
  // 保存当前数据
  await saveExperimentData()

  if (totalSubsteps.value > 0 && currentSubstep.value > 1) {
    currentSubstep.value -= 1
  } else if (currentStep.value > 1) {
    currentStep.value -= 1
    // 如果上一主步骤有子步骤，从1开始
    currentSubstep.value = steps[currentStep.value - 1]?.substeps ? 1 : 1
  }

  // 更新步骤恢复系统
  updateStepRecoveryInfo()
}

const goNext = async () => {
  // 保存当前数据
  await saveExperimentData()

  if (totalSubsteps.value > 0 && currentSubstep.value < totalSubsteps.value) {
    currentSubstep.value += 1
  } else if (currentStep.value < totalSteps) {
    currentStep.value += 1
    currentSubstep.value = steps[currentStep.value - 1]?.substeps ? 1 : 1
  }

  // 更新步骤恢复系统
  updateStepRecoveryInfo()
}

// 更新步骤恢复信息
const updateStepRecoveryInfo = () => {
  const currentStepData = steps[currentStep.value - 1]
  const hasValidation = (currentStep.value === 2 || currentStep.value === 3 || currentStep.value === 4 || currentStep.value === 7)
  let validated = true

  if (currentStep.value === 2 || currentStep.value === 3 || currentStep.value === 4) {
    validated = k1Valid.value
  } else if (currentStep.value === 7) {
    validated = Boolean(analysisResult.value)
  }

  stepRecovery.updateStepInfo({
    step: currentStep.value,
    substep: currentSubstep.value,
    title: currentStepData?.title || '',
    description: currentStepData?.description || '',
    hasValidation,
    validated
  })
}


// 当前步骤
const currentStep = ref(1)

// 步骤恢复功能
const stepRecovery = useStepRecovery({
  stuckDetection: {
    enabled: true,
    timeoutMs: 180000, // 3分钟
    retryAttempts: 2
  },
  validation: {
    allowSkip: true,
    skipWarning: '跳过验证可能影响实验结果，建议完成所有数据填写后再继续。确定要跳过吗？'
  },
  debug: {
    enabled: true,
    logStepChanges: true
  }
})

// 实验数据
const k1Data = ref<Array<{ratio: number, current: number | null}>>([
  { ratio: 0.0, current: null },
  { ratio: 0.1, current: null },
  { ratio: 0.2, current: null },
  { ratio: 0.3, current: null },
  { ratio: 0.4, current: null },
  { ratio: 0.5, current: null },
  { ratio: 0.6, current: null },
  { ratio: 0.7, current: null },
  { ratio: 0.8, current: null },
  { ratio: 0.9, current: null },
  { ratio: 1.0, current: null }
])

const k01Data = ref<Array<{ratio: number, current: number | null}>>([
  { ratio: 0.0, current: null },
  { ratio: 0.1, current: null },
  { ratio: 0.2, current: null },
  { ratio: 0.3, current: null },
  { ratio: 0.4, current: null },
  { ratio: 0.5, current: null },
  { ratio: 0.6, current: null },
  { ratio: 0.7, current: null },
  { ratio: 0.8, current: null },
  { ratio: 0.9, current: null },
  { ratio: 1.0, current: null }
])

// 学生信息
const plotting = ref(false)

const studentInfo = ref({
  studentId: '',
  name: ''
})

// 分析结果
const plotImageUrl = ref('')
const analysisResult = ref('')
const isPassed = ref(false)
const analyzing = ref(false)
const submitting = ref(false)

// 前端绘图相关
const showPlot = ref(false)
const plotData = ref<any>({})
const plotlyChart = ref()


const generateAnalysis = async () => {
  analyzing.value = true
  try {
    // AI分析
    const response = await experimentsApi.analyzeExperiment({
      experiment_code: 'current_control_circuit',
      data: {
        k1_current: k1Data.value.map(i => Number(i.current ?? 0)),
        k01_current: k01Data.value.map(i => Number(i.current ?? 0))
      }
    })

    analysisResult.value = response.analysis_result || '分析完成'
    isPassed.value = Boolean(response.is_passed)

    ElMessage.success('AI分析完成')
  } catch (error) {
    console.error('分析失败:', error)
    ElMessage.error('分析失败，请重试')
  } finally {
    analyzing.value = false
  }
}

const submitExperiment = async () => {
  submitting.value = true
  try {
    const response = await fetch(`${API_BASE}/api/experiments/submit`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        student_id: studentInfo.value.studentId,
        name: studentInfo.value.name,
        experiment_type_code: 'current_control_circuit',
        submission_data: {
          k1Data: k1Data.value,
          k01Data: k01Data.value
        }
      })
    })

    await response.json()

    ElMessage.success('实验报告提交成功！')

    // 提交成功后删除缓存数据
    try {
      await cacheService.deleteExperimentData(EXPERIMENT_TYPE)
      console.log('缓存数据已清理')
    } catch (error) {
      console.error('清理缓存失败:', error)
    }

    // 3秒后返回首页
    setTimeout(() => {
      router.push('/')
    }, 3000)

  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败，请重试')
  } finally {
    submitting.value = false
  }
}

const generatePlot = async () => {
  plotting.value = true
  try {
    // 使用前端plotly生成图表
    const chartConfig = generateCurrentControlCircuitPlot(
      {
        k1Data: k1Data.value,
        k01Data: k01Data.value
      },
      {
        width: 800,
        height: 600,
        smooth: true,
        lang: 'zh'
      }
    )

    plotData.value = chartConfig
    showPlot.value = true
    ElMessage.success('图形已生成')
  } catch (e) {
    console.error('生成图形失败:', e)
    ElMessage.error('生成图形失败，请重试')
  } finally {
    plotting.value = false
  }
}

// Plotly图表事件处理
const onPlotReady = (plotElement: any) => {
  console.log('图表已准备就绪:', plotElement)
}

const onPlotError = (error: Error) => {
  console.error('图表错误:', error)
  ElMessage.error('图表显示失败')
}

// 生命周期钩子
onMounted(async () => {
  // 检查是否有缓存数据
  try {
    const cachedData = await cacheService.getExperimentData(EXPERIMENT_TYPE)

    if (cachedData && Object.keys(cachedData).length > 0) {
      // 显示恢复对话框
      recoveryData.value = cachedData
      recoveryTimestamp.value = cachedData.timestamp || new Date().toISOString()
      showRecoveryDialog.value = true
    }
  } catch (error) {
    console.error('检查缓存数据失败:', error)
  }

  // 启动自动保存
  cacheService.startAutoSave(EXPERIMENT_TYPE, getCurrentExperimentData)

  // 初始化步骤恢复信息
  updateStepRecoveryInfo()
})

onUnmounted(() => {
  // 停止自动保存
  cacheService.stopAutoSave()

  // 页面卸载前保存数据
  if (autoSaveEnabled.value) {
    const currentData = getCurrentExperimentData()
    cacheService.saveBeforeUnload(EXPERIMENT_TYPE, currentData)
  }

  // 清理步骤恢复系统
  stepRecovery.cleanup()
})

// 数据恢复处理函数
const handleDataRestore = (data: any) => {
  restoreExperimentData(data)
  showRecoveryDialog.value = false
}

const handleStartNew = async () => {
  try {
    await cacheService.deleteExperimentData(EXPERIMENT_TYPE)
    showRecoveryDialog.value = false
  } catch (error) {
    console.error('清除缓存失败:', error)
  }
}

// 步骤恢复处理函数
const getStepProblemDescription = () => {
  const step = currentStep.value
  const substep = currentSubstep.value

  if (step === 2 && !k1Valid.value) {
    return '电路连接步骤需要完成所有数据填写才能继续'
  } else if (step === 3 && substep && !k1Valid.value) {
    return `k=1测量步骤 ${substep} 需要填写完整的电流数据`
  } else if (step === 4 && !k01Valid.value) {
    return 'k=0.1测量步骤需要填写完整的电流数据'
  } else if (step === 5 && !analysisResult.value) {
    return '数据分析步骤需要生成分析结果才能继续'
  }

  return '当前步骤可能存在验证问题，请检查是否完成了所有必要操作'
}

const handleResetStep = async () => {
  const step = currentStep.value

  if (step === 2 || step === 3) {
    // 重置k1数据
    k1Data.value.forEach(item => item.current = null)
    ElMessage.success('已重置k=1电流数据')
  } else if (step === 4) {
    // 重置k01数据
    k01Data.value.forEach(item => item.current = null)
    ElMessage.success('已重置k=0.1电流数据')
  } else if (step === 5) {
    // 重置分析结果
    analysisResult.value = ''
    plotData.value = null
    showPlot.value = false
    ElMessage.success('已重置分析结果')
  }

  await stepRecovery.resetCurrentStep()
}

const handleSkipValidation = async () => {
  try {
    await stepRecovery.skipValidation()
    // 强制设置验证通过
    if (currentStep.value === 2 || currentStep.value === 3) {
      // 对于数据输入步骤，填充默认值
      k1Data.value.forEach((item, index) => {
        if (item.current === null) {
          item.current = parseFloat((0.001 * (index + 1)).toFixed(4)) // 默认递增值
        }
      })
    } else if (currentStep.value === 4) {
      k01Data.value.forEach((item, index) => {
        if (item.current === null) {
          item.current = parseFloat((0.0001 * (index + 1)).toFixed(5)) // 默认递增值
        }
      })
    } else if (currentStep.value === 5) {
      analysisResult.value = '已跳过验证，请稍后完善分析结果'
    }
  } catch (error) {
    console.error('跳过验证失败:', error)
  }
}

const handleShowHelp = () => {
  const step = currentStep.value
  const helpContent = steps[step - 1]?.instruction || steps[step - 1]?.description || '暂无帮助信息'

  ElMessageBox.alert(helpContent, `步骤 ${step} 帮助`, {
    confirmButtonText: '知道了',
    type: 'info'
  })
}

// 监听数据变化，进行自动保存
watch([k1Data, k01Data, studentInfo], async () => {
  if (autoSaveEnabled.value) {
    await saveExperimentData()
  }
}, { deep: true }) // 深度监听

</script>

<style scoped>
/* 基础布局 */
.experiment-page {
  min-height: 100vh;
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
  background: #f5f5f5;
}

/* 实验标题 */
.experiment-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 30px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
}

.experiment-header h1 {
  font-size: 2.2rem;
  margin-bottom: 12px;
  font-weight: 600;
}

.experiment-header p {
  font-size: 1.1rem;
  opacity: 0.9;
}



/* 实验内容 */
.experiment-content {
  margin-bottom: 30px;
}

.step-section {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.step-section h2 {
  color: #333;
  margin-bottom: 20px;
  font-size: 1.5rem;
  border-bottom: 2px solid #667eea;
  padding-bottom: 10px;
}

.step-content {
  margin-bottom: 30px;
}

.step-content h3 {
  color: #555;
  margin: 20px 0 12px 0;
  font-size: 1.2rem;
}

.step-content ul {
  margin: 12px 0;
  padding-left: 20px;
}

.step-content li {
  margin: 8px 0;
  color: #666;
  line-height: 1.5;
}

/* 数据表格 */
.data-table {
  margin: 20px 0;
}

.data-table table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 12px;
}

.data-table th,
.data-table td {
  padding: 12px;
  text-align: center;
  border: 1px solid #ddd;
}

.data-table th {
  background: #f8f9fa;
  font-weight: 600;
  color: #333;
}

.data-table td {
  background: white;
}

/* 图表容器 */
.chart-container {
  text-align: center;
  margin: 20px 0;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.chart-image {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 分析结果 */
.analysis-result {
  margin: 20px 0;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.analysis-details {
  margin-top: 15px;
}

.analysis-details h2,
.analysis-details h3,
.analysis-details h4 {
  color: #333;
  margin: 15px 0 10px 0;
}

.analysis-details table {
  width: 100%;
  border-collapse: collapse;
  margin: 15px 0;
}

.analysis-details table th,
.analysis-details table td {
  border: 1px solid #ddd;
  padding: 8px 12px;
  text-align: left;
}

.analysis-details table th {
  background-color: #f5f5f5;
  font-weight: 600;
}

.analysis-details ul {
  margin: 10px 0;
  padding-left: 20px;
}

.analysis-details li {
  margin: 5px 0;
}

/* 详细步骤样式 */
.step-description {
  margin: 15px 0;
  padding: 15px;
  background: #e3f2fd;
  border-radius: 8px;
  border-left: 4px solid #2196f3;
}

.substep-content {
  margin: 20px 0;
}

.substep-item {
  margin-bottom: 20px;
}

.substep-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.substep-number {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: #667eea;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  margin-right: 15px;
}

.substep-title {
  color: #333;
  margin: 0;
  font-size: 1.2rem;
}

.substep-body {
  margin-left: 45px;
}

.substep-content-text {
  margin: 10px 0;
  color: #555;
  line-height: 1.6;
}

.checklist {
  margin: 15px 0;
  padding: 15px;
  background: #f0f8ff;
  border-radius: 6px;
  border-left: 3px solid #4caf50;
}

.checklist h4 {
  margin: 0 0 10px 0;
  color: #4caf50;
  font-size: 1rem;
}

.checklist ul {
  margin: 0;
  padding-left: 20px;
}

.checklist li {
  margin: 5px 0;
  color: #333;
}

.instruction {
  margin: 15px 0;
  padding: 15px;
  background: #fff3cd;
  border-radius: 6px;
  border-left: 3px solid #ffc107;
}

.instruction h4 {
  margin: 0 0 10px 0;
  color: #856404;
  font-size: 1rem;
}

.instruction p {
  margin: 0;
  color: #856404;
  line-height: 1.6;
}

.warning {
  margin: 15px 0;
  padding: 15px;
  background: #f8d7da;
  border-radius: 6px;
  border-left: 3px solid #dc3545;
}

.warning h4 {
  margin: 0 0 10px 0;
  color: #721c24;
  font-size: 1rem;
}

.warning p {
  margin: 0;
  color: #721c24;
  line-height: 1.6;
}

.success-message {
  margin: 15px 0;
  padding: 15px;
  background: #d4edda;
  border-radius: 6px;
  border-left: 3px solid #28a745;
  color: #155724;
}

.circuit-diagram {
  margin: 20px 0;
  text-align: center;
}

.diagram-placeholder {
  padding: 40px;
  background: #f8f9fa;
  border: 2px dashed #dee2e6;
  border-radius: 8px;
  color: #6c757d;
}

.analysis-content p {
  margin: 8px 0;
  color: #333;
}

/* 提交表单 */
.submit-form {
  margin: 20px 0;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.experiment-summary {
  margin: 20px 0;
  padding: 20px;
  background: #f0f8ff;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.experiment-summary p {
  margin: 8px 0;
  color: #333;
}

/* 步骤操作 */
.step-actions {
  display: flex;
  justify-content: space-between;
  gap: 12px;
}

/* 步骤指示器 */
.step-indicator {
  text-align: center;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.steps {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-bottom: 12px;
}

.step-item {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #e0e0e0;
  color: #666;
  font-weight: 600;
  transition: all 0.3s ease;
}

.step-item.active {
  background: #667eea;
  color: white;
}

.step-item.completed {
  background: #4caf50;
  color: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .experiment-page {
    padding: 15px;
  }

  .experiment-header {
    padding: 20px;
  }

  .experiment-header h1 {
    font-size: 1.8rem;
  }

  .step-section {
    padding: 20px;
  }

  .step-actions {
    flex-direction: column;
  }

  .steps {
    gap: 12px;
  }

  .step-item {
    width: 35px;
    height: 35px;
  }
}

@media (max-width: 480px) {
  .experiment-page {
    padding: 10px;
  }

  .experiment-header {
    padding: 15px;
  }

  .experiment-header h1 {
    font-size: 1.5rem;
  }

  .step-section {
    padding: 15px;
  }

  .data-table th,
  .data-table td {
    padding: 8px;
    font-size: 0.9rem;
  }
}
</style>
