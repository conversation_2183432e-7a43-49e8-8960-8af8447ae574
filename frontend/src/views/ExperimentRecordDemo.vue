<template>
  <div class="experiment-record-demo">
    <div class="demo-header">
      <h1>🔬 实验记录表系统演示</h1>
      <p>展示修复后的后台实验记录查看功能</p>
    </div>

    <div class="demo-section">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>📋 模拟后台实验记录列表</span>
            <el-button type="primary" @click="showRecordDetail(mockRecord)">
              查看记录详情
            </el-button>
          </div>
        </template>
        
        <el-table :data="[mockRecord]" style="width: 100%">
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="student_name" label="学生姓名" width="120" />
          <el-table-column prop="student_no" label="学号" width="120" />
          <el-table-column prop="experiment_name" label="实验名称" width="150" />
          <el-table-column prop="experiment_code" label="实验代码" width="180" />
          <el-table-column prop="submitted_at" label="提交时间" width="180" />
          <el-table-column prop="score" label="得分" width="80" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.status === 'approved' ? 'success' : 'warning'">
                {{ scope.row.status === 'approved' ? '已通过' : '待审核' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120">
            <template #default="scope">
              <el-button 
                type="primary" 
                size="small" 
                @click="showRecordDetail(scope.row)"
              >
                查看详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

    <!-- 实验记录详情弹窗 -->
    <ExperimentRecordDetailModal
      v-model:visible="showDetailModal"
      :record="selectedRecord"
      @close="handleDetailModalClose"
    />

    <div class="demo-info">
      <el-alert
        title="✅ 修复完成"
        type="success"
        :closable="false"
        show-icon
      >
        <template #default>
          <div>
            <p><strong>问题：</strong>后台不能查看实验记录</p>
            <p><strong>原因：</strong>ExperimentRecordDetailModal 组件使用了旧的解析逻辑，没有使用新的工厂模式组件</p>
            <p><strong>解决方案：</strong></p>
            <ul>
              <li>✅ 更新 ExperimentRecordDetailModal.vue 使用 ExperimentRecordTableFactory</li>
              <li>✅ 简化组件逻辑，移除重复的数据解析代码</li>
              <li>✅ 修复类型错误和构建问题</li>
              <li>✅ 保持导出功能正常工作</li>
            </ul>
            <p><strong>现在可以正常查看所有类型的实验记录了！</strong></p>
          </div>
        </template>
      </el-alert>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import ExperimentRecordDetailModal from '@/components/ExperimentRecordDetailModal.vue'
import type { ExperimentRecord } from '@/services/experimentDataParser'

const showDetailModal = ref(false)
const selectedRecord = ref<ExperimentRecord | null>(null)

// 模拟实验记录数据
const mockRecord = {
  id: 1001,
  student_name: '张三',
  student_no: '2021001',
  experiment_name: '制流电路实验',
  experiment_code: 'current_control_circuit',
  submitted_at: '2024-09-11 15:30:00',
  score: 95,
  status: 'approved',
  is_passed: true,
  submission_data: JSON.stringify({
    "实验数据": {
      "k=1电流数据": [
        { "比例": 0.1, "电流": 0.0012 },
        { "比例": 0.2, "电流": 0.0024 },
        { "比例": 0.3, "电流": 0.0036 },
        { "比例": 0.4, "电流": 0.0048 },
        { "比例": 0.5, "电流": 0.0060 }
      ],
      "k=0.1电流数据": [
        { "比例": 0.1, "电流": 0.00012 },
        { "比例": 0.2, "电流": 0.00024 },
        { "比例": 0.3, "电流": 0.00036 },
        { "比例": 0.4, "电流": 0.00048 },
        { "比例": 0.5, "电流": 0.00060 }
      ],
      "实验环境": {
        "温度": 25.5,
        "湿度": 60,
        "备注": "实验过程正常，数据稳定"
      }
    },
    "实验图形": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",
    "实验分析": "制流电路实验数据符合预期，k=1和k=0.1两种情况下的电流变化趋势正确，实验结果准确。"
  }),
  plot_data: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",
  analysis_result: "制流电路实验数据符合预期，k=1和k=0.1两种情况下的电流变化趋势正确，实验结果准确。"
}

const showRecordDetail = (record: any) => {
  // 转换为 ExperimentRecord 格式
  const experimentRecord: ExperimentRecord = {
    id: record.id,
    student_id: undefined,
    experiment_type_id: 1,
    experiment_code: record.experiment_code,
    experiment_name: record.experiment_name,
    submission_data: record.submission_data,
    plot_data: record.plot_data,
    analysis_result: record.analysis_result,
    is_passed: record.is_passed,
    score: record.score,
    submitted_at: record.submitted_at,
    status: record.status
  }

  selectedRecord.value = experimentRecord
  showDetailModal.value = true
}

const handleDetailModalClose = () => {
  showDetailModal.value = false
  selectedRecord.value = null
}
</script>

<style scoped>
.experiment-record-demo {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.demo-header {
  text-align: center;
  margin-bottom: 30px;
}

.demo-header h1 {
  color: #2c3e50;
  margin-bottom: 10px;
}

.demo-header p {
  color: #7f8c8d;
  margin: 0;
}

.demo-section {
  margin-bottom: 30px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.demo-info {
  margin-top: 30px;
}

.demo-info ul {
  margin: 10px 0;
  padding-left: 20px;
}

.demo-info li {
  margin: 5px 0;
}
</style>
