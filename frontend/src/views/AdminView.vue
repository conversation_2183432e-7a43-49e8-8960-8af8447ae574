<template>
  <div class="admin-view">
    <div class="admin-header">
      <h1>管理后台</h1>
      <div class="admin-user-info">
        <span>欢迎，{{ authStore.user?.name }}</span>
        <el-button type="primary" @click="authStore.logout">退出登录</el-button>
      </div>
    </div>
    
    <div class="admin-content">
      <el-row :gutter="20">
        <!-- 侧边栏 -->
        <el-col :span="4">
          <el-menu
            :default-active="activeMenu"
            class="admin-menu"
            @select="handleMenuSelect"
          >
            <el-menu-item index="dashboard">
              <el-icon><DataBoard /></el-icon>
              <span>仪表盘</span>
            </el-menu-item>
            <el-menu-item index="experiments">
              <el-icon><Histogram /></el-icon>
              <span>实验管理</span>
            </el-menu-item>
            <el-menu-item index="students">
              <el-icon><User /></el-icon>
              <span>学生管理</span>
            </el-menu-item>
            <el-menu-item index="classes">
              <el-icon><School /></el-icon>
              <span>班级管理</span>
            </el-menu-item>
            <el-menu-item index="learning-stats">
              <el-icon><TrendCharts /></el-icon>
              <span>学习统计</span>
            </el-menu-item>
            <el-menu-item index="records">
              <el-icon><Document /></el-icon>
              <span>实验记录</span>
            </el-menu-item>
          </el-menu>
        </el-col>
        
        <!-- 主内容区 -->
        <el-col :span="20">
          <div class="admin-main">
            <!-- 仪表盘 -->
            <div v-show="activeMenu === 'dashboard'" class="admin-panel">
              <AdminDashboard />
            </div>
            
            <!-- 实验管理 -->
            <div v-show="activeMenu === 'experiments'" class="admin-panel">
              <AdminExperiments />
            </div>
            
            <!-- 学生管理 -->
            <div v-show="activeMenu === 'students'" class="admin-panel">
              <AdminStudents />
            </div>
            
            <!-- 班级管理 -->
            <div v-show="activeMenu === 'classes'" class="admin-panel">
              <AdminClasses />
            </div>

            <!-- 学习统计 -->
            <div v-show="activeMenu === 'learning-stats'" class="admin-panel">
              <AdminLearningStats />
            </div>

            <!-- 实验记录 -->
            <div v-show="activeMenu === 'records'" class="admin-panel">
              <AdminRecords />
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { DataBoard, Histogram, User, School, Document, TrendCharts } from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'
import AdminDashboard from '@/components/admin/AdminDashboard.vue'
import AdminExperiments from '@/components/admin/AdminExperiments.vue'
import AdminStudents from '@/components/admin/AdminStudents.vue'
import AdminClasses from '@/components/admin/AdminClasses.vue'
import AdminLearningStats from '@/components/admin/AdminLearningStats.vue'
import AdminRecords from '@/components/admin/AdminRecords.vue'

const authStore = useAuthStore()

// 响应式数据
const activeMenu = ref('dashboard')

// 处理菜单选择
const handleMenuSelect = (index: string) => {
  activeMenu.value = index
}
</script>

<style scoped>
.admin-view {
  min-height: 100vh;
  background-color: #f5f7fa;
}

.admin-header {
  background: white;
  padding: 20px 30px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.admin-header h1 {
  margin: 0;
  color: #303133;
  font-size: 1.8rem;
}

.admin-user-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.admin-user-info span {
  color: #606266;
}

.admin-content {
  padding: 20px;
}

.admin-menu {
  height: calc(100vh - 120px);
  border-right: 1px solid #e4e7ed;
}

.admin-main {
  background: white;
  border-radius: 8px;
  min-height: calc(100vh - 120px);
}

.admin-panel {
  padding: 20px;
}

:deep(.el-menu-item) {
  height: 50px;
  line-height: 50px;
}

:deep(.el-menu-item.is-active) {
  background-color: #ecf5ff;
  color: #409eff;
}
</style>
