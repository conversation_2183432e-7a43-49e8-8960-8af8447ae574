<template>
  <div class="home-page">
    <!-- 页面标题 -->
    <header class="page-header">
      <div class="header-content">
        <h1>大学物理实验</h1>
        <p>专业的物理实验指导系统，提供完整的实验流程、数据分析和智能评估</p>
      </div>
    </header>

    <!-- 主要内容 -->
    <main class="main-content">
      <!-- 实验列表 -->
      <section class="experiments-section">
        <h2>实验项目</h2>
        <div class="experiments-list">
          <!-- 制流电路实验 -->
          <div class="experiment-item" @click="goToExperiment('current-control-circuit')">
            <div class="experiment-info">
              <h3>制流电路实验</h3>
              <p>通过测量不同接入比例下的电流变化，掌握制流电路的工作原理和特性</p>
              <div class="experiment-tags">
                <el-tag type="success">可用</el-tag>
                <el-tag>基础难度</el-tag>
                <el-tag>60分钟</el-tag>
              </div>
            </div>
            <div class="experiment-action">
              <el-button type="primary">开始实验</el-button>
            </div>
          </div>

          <!-- 示波器实验 -->
          <div class="experiment-item disabled">
            <div class="experiment-info">
              <h3>示波器实验</h3>
              <p>学习示波器的使用方法，观察和测量各种电信号的波形特征</p>
              <div class="experiment-tags">
                <el-tag type="info">开发中</el-tag>
                <el-tag>中级难度</el-tag>
                <el-tag>90分钟</el-tag>
              </div>
            </div>
            <div class="experiment-action">
              <el-button disabled>敬请期待</el-button>
            </div>
          </div>
        </div>
      </section>

      <!-- 平台特色 -->
      <section class="features-section">
        <h2>平台特色</h2>
        <div class="features-list">
          <div class="feature-item">
            <h3>步骤指导</h3>
            <p>详细的实验步骤说明，确保每个环节都能正确执行</p>
          </div>
          <div class="feature-item">
            <h3>AI智能分析</h3>
            <p>基于Gemini 2.5 Flash的智能数据分析，自动判断实验结果</p>
          </div>
          <div class="feature-item">
            <h3>前端绘图</h3>
            <p>使用Plotly.js进行前端交互式图表生成，提升用户体验</p>
          </div>
          <div class="feature-item">
            <h3>个性化指导</h3>
            <p>根据学生水平提供个性化的实验指导和建议</p>
          </div>
        </div>
      </section>

      <!-- 开发者工具 -->
      <section class="dev-tools-section">
        <h2>开发者工具</h2>
        <div class="dev-tools-list">
          <el-button @click="goToTestPage('test')" type="info">基础测试</el-button>
          <el-button @click="goToTestPage('test-experiment-record')" type="info">实验记录表测试</el-button>
          <el-button @click="goToTestPage('test-ai-analysis')" type="warning">AI分析功能测试</el-button>
          <el-button @click="goToTestPage('demo-experiment-record')" type="success">实验记录系统演示</el-button>
        </div>
      </section>
    </main>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()

// 跳转到实验页面
const goToExperiment = (experimentCode: string) => {
  if (experimentCode === 'current-control-circuit') {
    router.push(`/experiments/${experimentCode}`)
  }
}

// 跳转到测试页面
const goToTestPage = (pageName: string) => {
  router.push(`/${pageName}`)
}
</script>

<style scoped>
/* 基础重置和布局 */
.home-page {
  min-height: 100vh;
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: 40px;
  padding: 40px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
}

.header-content h1 {
  font-size: 2.5rem;
  margin-bottom: 16px;
  font-weight: 600;
}

.header-content p {
  font-size: 1.1rem;
  opacity: 0.9;
  max-width: 600px;
  margin: 0 auto;
}

/* 主要内容 */
.main-content {
  display: flex;
  flex-direction: column;
  gap: 40px;
}

/* 实验列表 */
.experiments-section h2,
.features-section h2 {
  font-size: 1.8rem;
  margin-bottom: 24px;
  color: #333;
  text-align: center;
}

.experiments-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.experiment-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.experiment-item:hover {
  border-color: #667eea;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
}

.experiment-item.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.experiment-item.disabled:hover {
  border-color: #e0e0e0;
  box-shadow: none;
}

.experiment-info h3 {
  font-size: 1.3rem;
  margin-bottom: 8px;
  color: #333;
}

.experiment-info p {
  color: #666;
  margin-bottom: 12px;
  line-height: 1.5;
}

.experiment-tags {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.experiment-action {
  flex-shrink: 0;
}

/* 特色功能 */
.features-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.feature-item {
  padding: 20px;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  text-align: center;
}

.feature-item h3 {
  font-size: 1.2rem;
  margin-bottom: 12px;
  color: #333;
}

.feature-item p {
  color: #666;
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .home-page {
    padding: 15px;
  }
  
  .page-header {
    padding: 30px 15px;
  }
  
  .header-content h1 {
    font-size: 2rem;
  }
  
  .experiment-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .experiment-action {
    align-self: stretch;
  }
  
  .features-list {
    grid-template-columns: 1fr;
  }
}

/* 开发者工具样式 */
.dev-tools-section {
  margin-top: 40px;
  padding: 30px;
  background-color: #f8f9fa;
  border-radius: 12px;
  border: 2px dashed #dee2e6;
}

.dev-tools-section h2 {
  color: #6c757d;
  margin-bottom: 20px;
  font-size: 1.5rem;
}

.dev-tools-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.dev-tools-list .el-button {
  margin: 0;
}

@media (max-width: 480px) {
  .home-page {
    padding: 10px;
  }
  
  .header-content h1 {
    font-size: 1.8rem;
  }
  
  .experiment-item {
    padding: 20px;
  }
}
</style>
