<template>
  <div class="test-experiment-record-view">
    <div class="page-header">
      <h1>实验记录表系统测试</h1>
      <p>测试不同实验类型的记录表显示</p>
    </div>

    <div class="test-controls">
      <el-card>
        <template #header>
          <span>选择测试实验类型</span>
        </template>
        <el-radio-group v-model="selectedExperimentType" @change="loadTestData">
          <el-radio value="current_control_circuit">制流电路实验</el-radio>
          <el-radio value="oscilloscope">示波器实验</el-radio>
          <el-radio value="unknown">未知实验类型</el-radio>
        </el-radio-group>
      </el-card>
    </div>

    <div class="test-content">
      <ExperimentRecordTableFactory
        v-if="testRecord"
        :record="testRecord"
        :experiment-type="selectedExperimentType"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import ExperimentRecordTableFactory from '@/components/experiment-details/ExperimentRecordTableFactory.vue'
import type { ExperimentRecord } from '@/services/experimentDataParser'

const selectedExperimentType = ref('current_control_circuit')
const testRecord = ref<ExperimentRecord | null>(null)

// 测试数据
const testData = {
  current_control_circuit: {
    student_id: "2021001",
    student_name: "张三",
    k1Data: [
      { ratio: 0.1, current: 0.0012 },
      { ratio: 0.2, current: 0.0024 },
      { ratio: 0.3, current: 0.0036 },
      { ratio: 0.4, current: 0.0048 },
      { ratio: 0.5, current: 0.0060 }
    ],
    k01Data: [
      { ratio: 0.1, current: 0.00012 },
      { ratio: 0.2, current: 0.00024 },
      { ratio: 0.3, current: 0.00036 },
      { ratio: 0.4, current: 0.00048 },
      { ratio: 0.5, current: 0.00060 }
    ],
    temperature: 25.5,
    humidity: 60,
    notes: "实验过程正常，数据稳定"
  },
  oscilloscope: {
    student_id: "2021002",
    student_name: "李四",
    frequency: 1000,
    amplitude: 5.0,
    waveform_type: "正弦波",
    measurements: [
      { time: 0.000000, voltage: 0.0000 },
      { time: 0.000100, voltage: 2.9389 },
      { time: 0.000200, voltage: 4.7553 },
      { time: 0.000300, voltage: 4.7553 },
      { time: 0.000400, voltage: 2.9389 },
      { time: 0.000500, voltage: 0.0000 },
      { time: 0.000600, voltage: -2.9389 },
      { time: 0.000700, voltage: -4.7553 },
      { time: 0.000800, voltage: -4.7553 },
      { time: 0.000900, voltage: -2.9389 },
      { time: 0.001000, voltage: 0.0000 }
    ],
    notes: "波形稳定，无明显失真"
  },
  unknown: {
    student_id: "2021003",
    student_name: "王五",
    experiment_parameter1: 123.45,
    experiment_parameter2: "测试值",
    measurements: [
      { item: "测量1", value: 67.89, unit: "V" },
      { item: "测量2", value: 98.76, unit: "A" }
    ],
    notes: "通用格式实验数据"
  }
}

const loadTestData = () => {
  const data = testData[selectedExperimentType.value as keyof typeof testData]
  
  testRecord.value = {
    id: Math.floor(Math.random() * 1000),
    student_id: 1,
    experiment_type_id: 1,
    experiment_code: selectedExperimentType.value,
    experiment_name: getExperimentName(selectedExperimentType.value),
    submission_data: JSON.stringify(data),
    plot_data: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",
    analysis_result: `这是${getExperimentName(selectedExperimentType.value)}的AI分析结果示例。实验数据符合预期，测量结果准确。`,
    is_passed: true,
    score: 95,
    submitted_at: new Date().toISOString(),
    status: "approved"
  }
}

const getExperimentName = (code: string) => {
  const nameMap: Record<string, string> = {
    'current_control_circuit': '制流电路实验',
    'oscilloscope': '示波器实验',
    'unknown': '未知实验类型'
  }
  return nameMap[code] || code
}

onMounted(() => {
  loadTestData()
})
</script>

<style scoped>
.test-experiment-record-view {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
}

.page-header h1 {
  color: #2c3e50;
  margin-bottom: 10px;
}

.page-header p {
  color: #7f8c8d;
  margin: 0;
}

.test-controls {
  margin-bottom: 30px;
}

.test-content {
  background-color: #f5f7fa;
  border-radius: 8px;
  padding: 20px;
}
</style>
