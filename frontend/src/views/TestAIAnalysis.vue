<template>
  <div class="test-ai-analysis">
    <h1>AI分析功能测试</h1>
    
    <div class="test-section">
      <h2>制流电路实验数据</h2>
      
      <div class="data-input">
        <div class="input-group">
          <label>k=1时的电流值 (mA):</label>
          <el-input
            v-model="k1CurrentStr"
            type="textarea"
            :rows="3"
            placeholder="请输入11个电流值，用逗号分隔"
          />
        </div>
        
        <div class="input-group">
          <label>k=0.1时的电流值 (mA):</label>
          <el-input
            v-model="k01CurrentStr"
            type="textarea"
            :rows="3"
            placeholder="请输入11个电流值，用逗号分隔"
          />
        </div>
        
        <el-button 
          type="primary" 
          @click="analyzeData"
          :loading="analyzing"
          :disabled="!canAnalyze"
        >
          {{ analyzing ? '分析中...' : '开始AI分析' }}
        </el-button>
      </div>
      
      <div v-if="analysisResult" class="analysis-result">
        <h3>分析结果</h3>
        <div class="result-content" v-html="analysisResult"></div>
        <div class="result-status">
          <el-tag :type="isPassed ? 'success' : 'danger'">
            {{ isPassed ? '实验通过' : '实验未通过' }}
          </el-tag>
        </div>
      </div>
      
      <div v-if="error" class="error-message">
        <el-alert
          :title="error"
          type="error"
          show-icon
          :closable="false"
        />
      </div>
    </div>
    
    <div class="preset-data">
      <h3>预设测试数据</h3>
      <el-button @click="loadGoodData" type="success" size="small">
        加载正常数据
      </el-button>
      <el-button @click="loadBadData" type="warning" size="small">
        加载异常数据
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { experimentsApi } from '@/api/experiments'

const k1CurrentStr = ref('12.0, 10.9, 9.8, 8.7, 7.6, 6.5, 5.4, 4.3, 3.2, 2.1, 1.0')
const k01CurrentStr = ref('12.0, 10.8, 9.6, 8.4, 7.2, 6.0, 4.8, 3.6, 2.4, 1.2, 0.6')
const analyzing = ref(false)
const analysisResult = ref('')
const isPassed = ref(false)
const error = ref('')

const canAnalyze = computed(() => {
  return k1CurrentStr.value.trim() && k01CurrentStr.value.trim() && !analyzing.value
})

const parseCurrentData = (str: string): number[] => {
  return str.split(',').map(s => parseFloat(s.trim())).filter(n => !isNaN(n))
}

const analyzeData = async () => {
  try {
    analyzing.value = true
    error.value = ''
    analysisResult.value = ''
    
    const k1Current = parseCurrentData(k1CurrentStr.value)
    const k01Current = parseCurrentData(k01CurrentStr.value)
    
    if (k1Current.length !== 11 || k01Current.length !== 11) {
      throw new Error('每组数据必须包含11个数值')
    }
    
    const response = await experimentsApi.analyzeExperiment({
      experiment_code: 'current_control_circuit',
      data: {
        k1_current: k1Current,
        k01_current: k01Current
      }
    })

    analysisResult.value = response.analysis_result
    isPassed.value = response.is_passed ?? false
    
    ElMessage.success('AI分析完成')
    
  } catch (err: any) {
    error.value = err.response?.data?.detail || err.message || '分析失败'
    ElMessage.error('分析失败')
  } finally {
    analyzing.value = false
  }
}

const loadGoodData = () => {
  k1CurrentStr.value = '12.0, 10.9, 9.8, 8.7, 7.6, 6.5, 5.4, 4.3, 3.2, 2.1, 1.0'
  k01CurrentStr.value = '12.0, 10.8, 9.6, 8.4, 7.2, 6.0, 4.8, 3.6, 2.4, 1.2, 0.6'
}

const loadBadData = () => {
  k1CurrentStr.value = '12.0, 11.5, 11.0, 10.5, 10.0, 9.5, 9.0, 8.5, 8.0, 7.5, 7.0'
  k01CurrentStr.value = '12.0, 15.0, 18.0, 21.0, 24.0, 27.0, 30.0, 33.0, 36.0, 39.0, 42.0'
}
</script>

<style scoped>
.test-ai-analysis {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.test-section {
  margin-bottom: 30px;
}

.data-input {
  margin-bottom: 20px;
}

.input-group {
  margin-bottom: 15px;
}

.input-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.analysis-result {
  margin-top: 20px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background-color: #f9f9f9;
}

.result-content {
  margin-bottom: 15px;
  line-height: 1.6;
}

.result-status {
  text-align: center;
}

.error-message {
  margin-top: 20px;
}

.preset-data {
  padding: 15px;
  background-color: #f5f5f5;
  border-radius: 8px;
}

.preset-data h3 {
  margin-bottom: 10px;
}

.preset-data .el-button {
  margin-right: 10px;
}
</style>
