<template>
  <div class="test-container">
    <h1>测试页面</h1>
    <p>如果您能看到这个页面，说明Vue正常工作</p>
    <el-button type="primary" @click="testClick">测试按钮</el-button>
    <p v-if="clicked">按钮已点击！</p>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const clicked = ref(false)

const testClick = () => {
  clicked.value = true
}
</script>

<style scoped>
.test-container {
  padding: 20px;
  text-align: center;
}
</style>
