<template>
  <div class="profile-view">
    <div class="profile-header">
      <h1>个人信息</h1>
      <p>查看和编辑您的个人资料</p>
    </div>
    
    <div class="profile-content">
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="profile-card">
            <div class="avatar-section">
              <el-avatar :size="100" :icon="UserFilled" />
              <h3>{{ authStore.user?.name }}</h3>
              <el-tag :type="authStore.isStudent ? 'primary' : 'success'">
                {{ authStore.isStudent ? '学生' : '管理员' }}
              </el-tag>
            </div>
          </div>
        </el-col>
        
        <el-col :span="16">
          <div class="info-card">
            <h3>基本信息</h3>
            <el-form
              ref="profileFormRef"
              :model="profileForm"
              :rules="profileRules"
              label-width="100px"
            >
              <el-form-item label="姓名" prop="name">
                <el-input v-model="profileForm.name" :disabled="!editing" />
              </el-form-item>
              
              <el-form-item v-if="authStore.isStudent" label="学号" prop="student_id">
                <el-input v-model="profileForm.student_id" disabled />
              </el-form-item>
              
              <el-form-item v-if="authStore.isStudent" label="班级" prop="class_name">
                <el-input v-model="profileForm.class_name" disabled />
              </el-form-item>
              
              <el-form-item v-if="authStore.isStudent" label="分组" prop="group_name">
                <el-input v-model="profileForm.group_name" disabled />
              </el-form-item>
              
              <el-form-item label="邮箱" prop="email">
                <el-input v-model="profileForm.email" :disabled="!editing" />
              </el-form-item>
              
              <el-form-item>
                <el-button v-if="!editing" type="primary" @click="startEdit">
                  编辑信息
                </el-button>
                <template v-else>
                  <el-button type="primary" @click="saveProfile" :loading="saving">
                    保存
                  </el-button>
                  <el-button @click="cancelEdit">取消</el-button>
                </template>
              </el-form-item>
            </el-form>
          </div>
        </el-col>
      </el-row>
      
      <!-- 实验记录 -->
      <div v-if="authStore.isStudent" class="experiment-records">
        <h3>我的实验记录</h3>
        <el-table :data="experimentRecords" style="width: 100%">
          <el-table-column prop="experiment_name" label="实验名称" />
          <el-table-column prop="submitted_at" label="提交时间" width="180" />
          <el-table-column prop="score" label="得分" width="80">
            <template #default="{ row }">
              {{ row.score || '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag
                :type="getStatusType(row.status)"
                size="small"
              >
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120">
            <template #default="{ row }">
              <el-button size="small" @click="viewExperiment(row)">查看</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { UserFilled } from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'

const authStore = useAuthStore()

// 响应式数据
const editing = ref(false)
const saving = ref(false)
const profileFormRef = ref<FormInstance>()

const profileForm = reactive({
  name: '',
  student_id: '',
  class_name: '',
  group_name: '',
  email: ''
})

const originalForm = reactive({
  name: '',
  student_id: '',
  class_name: '',
  group_name: '',
  email: ''
})

const experimentRecords = ref([
  {
    id: 1,
    experiment_name: '制流电路实验',
    submitted_at: '2024-01-15 14:30:00',
    score: 85,
    status: 'reviewed'
  }
])

// 表单验证规则
const profileRules: FormRules = {
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' },
    { min: 2, max: 10, message: '姓名长度应在2-10位之间', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ]
}

// 初始化表单数据
const initForm = () => {
  if (authStore.user) {
    const userInfo = authStore.user.additional_info || {}
    
    profileForm.name = authStore.user.name
    profileForm.student_id = userInfo.student_id || ''
    profileForm.class_name = userInfo.class_name || ''
    profileForm.group_name = userInfo.group_name || ''
    profileForm.email = userInfo.email || ''
    
    // 保存原始数据
    Object.assign(originalForm, profileForm)
  }
}

// 开始编辑
const startEdit = () => {
  editing.value = true
}

// 保存资料
const saveProfile = async () => {
  if (!profileFormRef.value) return
  
  try {
    await profileFormRef.value.validate()
    
    saving.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 更新用户信息
    authStore.updateUser({
      name: profileForm.name,
      additional_info: {
        ...authStore.user?.additional_info,
        email: profileForm.email
      }
    })
    
    Object.assign(originalForm, profileForm)
    editing.value = false
    
    ElMessage.success('个人信息更新成功')
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

// 取消编辑
const cancelEdit = () => {
  Object.assign(profileForm, originalForm)
  editing.value = false
}

// 获取状态类型
const getStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    submitted: 'warning',
    reviewed: 'success',
    approved: 'success',
    rejected: 'danger'
  }
  return typeMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    submitted: '已提交',
    reviewed: '已评审',
    approved: '已通过',
    rejected: '未通过'
  }
  return textMap[status] || status
}

// 查看实验
const viewExperiment = (record: any) => {
  ElMessage.info('查看功能开发中')
}

// 组件挂载时初始化
onMounted(() => {
  initForm()
})
</script>

<style scoped>
.profile-view {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.profile-header {
  text-align: center;
  margin-bottom: 30px;
}

.profile-header h1 {
  color: #303133;
  margin-bottom: 10px;
}

.profile-header p {
  color: #606266;
}

.profile-content {
  margin-bottom: 30px;
}

.profile-card, .info-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 30px;
}

.avatar-section {
  text-align: center;
}

.avatar-section h3 {
  margin: 20px 0 10px 0;
  color: #303133;
}

.info-card h3 {
  margin: 0 0 20px 0;
  color: #303133;
}

.experiment-records {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 30px;
  margin-top: 20px;
}

.experiment-records h3 {
  margin: 0 0 20px 0;
  color: #303133;
}
</style>
