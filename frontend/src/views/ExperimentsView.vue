<template>
  <div class="experiments-view">
    <div class="page-header">
      <h1>实验列表</h1>
      <p>选择您要进行的物理实验</p>
    </div>
    
    <div class="experiments-container">
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="3" animated />
      </div>
      
      <div v-else class="experiments-grid">
        <div
          v-for="experiment in experimentTypes"
          :key="experiment.code"
          class="experiment-card"
          @click="startExperiment(experiment)"
        >
          <div class="experiment-header">
            <div class="experiment-icon">
              <el-icon size="32"><Histogram /></el-icon>
            </div>
            <div class="experiment-status">
              <el-tag
                v-if="getExperimentStatus(experiment.code)"
                :type="getExperimentStatus(experiment.code) === 'completed' ? 'success' : 'warning'"
                size="small"
              >
                {{ getExperimentStatus(experiment.code) === 'completed' ? '已完成' : '进行中' }}
              </el-tag>
            </div>
          </div>
          
          <div class="experiment-content">
            <h3>{{ experiment.name }}</h3>
            <p>{{ experiment.description }}</p>
            
            <div class="experiment-meta">
              <div class="meta-item">
                <el-icon><Clock /></el-icon>
                <span>{{ experiment.duration_minutes }}分钟</span>
              </div>
            </div>
          </div>
          
          <div class="experiment-footer">
            <el-button type="primary" :icon="VideoPlay">
              {{ getExperimentStatus(experiment.code) ? '继续实验' : '开始实验' }}
            </el-button>
          </div>
        </div>
      </div>
      
      <div v-if="!loading && experimentTypes.length === 0" class="empty-state">
        <el-empty description="暂无可用实验" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Histogram, Clock, VideoPlay } from '@element-plus/icons-vue'
import type { ExperimentType } from '@/types'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const experimentTypes = ref<ExperimentType[]>([])

// 获取实验列表
const fetchExperiments = async () => {
  loading.value = true
  try {
    // 直接使用静态数据，不需要认证
    experimentTypes.value = [
      {
        id: 1,
        name: '制流电路实验',
        code: 'current_control_circuit',
        description: '通过测量不同k值下的电流变化，分析制流电路的特性',
        instructions: '本实验通过制流电路测量电流变化规律',
        duration_minutes: 60,
        max_score: 100,
        is_active: true
      }
    ]
  } catch (error) {
    ElMessage.error('获取实验列表失败')
  } finally {
    loading.value = false
  }
}

// 开始实验
const startExperiment = (experiment: ExperimentType) => {
  // 根据实验代码跳转到对应的实验页面
  const routeMap: Record<string, string> = {
    'current_control_circuit': '/experiments/current-control-circuit',
    'oscilloscope': '/experiments/oscilloscope'
  }
  
  const route = routeMap[experiment.code]
  if (route) {
    router.push(route)
  } else {
    ElMessage.warning('该实验暂未开放')
  }
}

// 获取实验状态（模拟）
const getExperimentStatus = (experimentCode: string) => {
  // 这里可以从本地存储或后端获取实验状态
  const status = localStorage.getItem(`experiment_status_${experimentCode}`)
  return status || null
}


// 组件挂载时获取数据
onMounted(() => {
  fetchExperiments()
})
</script>

<style scoped>
.experiments-view {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.page-header {
  text-align: center;
  margin-bottom: 40px;
}

.page-header h1 {
  font-size: 2.5rem;
  color: #303133;
  margin-bottom: 10px;
}

.page-header p {
  color: #606266;
  font-size: 1.1rem;
}

.experiments-container {
  min-height: 400px;
}

.loading-container {
  padding: 40px;
}

.experiments-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 30px;
}

.experiment-card {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  border: 2px solid transparent;
}

.experiment-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
  border-color: #409eff;
}

.experiment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.experiment-icon {
  color: #409eff;
}

.experiment-content h3 {
  font-size: 1.5rem;
  color: #303133;
  margin-bottom: 15px;
}

.experiment-content p {
  color: #606266;
  line-height: 1.6;
  margin-bottom: 20px;
}

.experiment-meta {
  display: flex;
  gap: 20px;
  margin-bottom: 25px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 5px;
  color: #909399;
  font-size: 0.9rem;
}

.experiment-footer {
  text-align: center;
}

.experiment-footer .el-button {
  width: 100%;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
}

@media (max-width: 768px) {
  .experiments-grid {
    grid-template-columns: 1fr;
  }
  
  .page-header h1 {
    font-size: 2rem;
  }
}
</style>
