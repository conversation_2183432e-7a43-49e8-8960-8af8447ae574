/**
 * 实验数据缓存服务
 * 用于自动保存学生实验数据，防止意外关闭页面导致数据丢失
 */

import axios, { type AxiosResponse } from 'axios'

// 类型定义
interface CacheResponse {
  success: boolean
  message: string
  data?: any
  session_id?: string
}

interface CacheInfo {
  session_id: string
  total_count: number
  cached_experiments: Array<{
    experiment_type: string
    timestamp: string
  }>
}

class CacheService {
  private baseURL: string
  private autoSaveInterval: number | null = null
  private autoSaveDelay: number = 30000 // 30秒自动保存一次

  constructor() {
    this.baseURL = import.meta.env.VITE_API_URL || 'http://localhost:30000'
  }

  /**
   * 保存实验数据到服务器缓存
   * @param experimentType - 实验类型
   * @param data - 实验数据
   * @param ttl - 缓存过期时间（秒）
   * @returns 保存结果
   */
  async saveExperimentData(experimentType: string, data: any, ttl: number = 7200): Promise<CacheResponse> {
    try {
      const response: AxiosResponse<CacheResponse> = await axios.post(`${this.baseURL}/api/cache/save`, {
        experiment_type: experimentType,
        data: data,
        ttl: ttl
      }, {
        withCredentials: true // 确保发送cookies
      })
      
      console.log('实验数据已自动保存到服务器缓存')
      return response.data
    } catch (error) {
      console.error('保存实验数据到缓存失败:', error)
      throw error
    }
  }

  /**
   * 从服务器缓存获取实验数据
   * @param experimentType - 实验类型
   * @returns 缓存的数据
   */
  async getExperimentData(experimentType: string): Promise<any | null> {
    try {
      const response: AxiosResponse<CacheResponse> = await axios.get(`${this.baseURL}/api/cache/get/${experimentType}`, {
        withCredentials: true
      })
      
      if (response.data.success) {
        console.log('从服务器缓存恢复实验数据')
        return response.data.data
      }
      return null
    } catch (error) {
      console.error('获取缓存数据失败:', error)
      return null
    }
  }

  /**
   * 删除服务器缓存中的实验数据
   * @param experimentType - 实验类型
   * @returns 删除结果
   */
  async deleteExperimentData(experimentType: string): Promise<boolean> {
    try {
      const response: AxiosResponse<CacheResponse> = await axios.delete(`${this.baseURL}/api/cache/delete/${experimentType}`, {
        withCredentials: true
      })
      
      console.log('服务器缓存数据已删除')
      return response.data.success
    } catch (error) {
      console.error('删除缓存数据失败:', error)
      return false
    }
  }

  /**
   * 获取当前会话的缓存信息
   * @returns 缓存信息
   */
  async getCacheInfo(): Promise<CacheInfo | null> {
    try {
      const response: AxiosResponse<{ data: CacheInfo }> = await axios.get(`${this.baseURL}/api/cache/info`, {
        withCredentials: true
      })
      
      return response.data.data
    } catch (error) {
      console.error('获取缓存信息失败:', error)
      return null
    }
  }

  /**
   * 启动自动保存
   * @param experimentType - 实验类型
   * @param getDataCallback - 获取当前数据的回调函数
   */
  startAutoSave(experimentType: string, getDataCallback: () => any): void {
    this.stopAutoSave() // 先停止之前的自动保存
    
    this.autoSaveInterval = window.setInterval(async () => {
      try {
        const currentData = getDataCallback()
        if (currentData && Object.keys(currentData).length > 0) {
          await this.saveExperimentData(experimentType, currentData)
        }
      } catch (error) {
        console.error('自动保存失败:', error)
      }
    }, this.autoSaveDelay)
    
    console.log(`已启动自动保存，每${this.autoSaveDelay / 1000}秒保存一次`)
  }

  /**
   * 停止自动保存
   */
  stopAutoSave(): void {
    if (this.autoSaveInterval) {
      clearInterval(this.autoSaveInterval)
      this.autoSaveInterval = null
      console.log('已停止自动保存')
    }
  }

  /**
   * 手动触发保存（用于"下一步"按钮等）
   * @param experimentType - 实验类型
   * @param data - 实验数据
   */
  async manualSave(experimentType: string, data: any): Promise<boolean> {
    try {
      await this.saveExperimentData(experimentType, data)
      return true
    } catch (error) {
      console.error('手动保存失败:', error)
      return false
    }
  }

  /**
   * 页面卸载前的保存
   * @param experimentType - 实验类型
   * @param data - 实验数据
   */
  saveBeforeUnload(experimentType: string, data: any): void {
    // 使用 navigator.sendBeacon 进行可靠的数据发送
    if (navigator.sendBeacon && data && Object.keys(data).length > 0) {
      const payload = JSON.stringify({
        experiment_type: experimentType,
        data: data,
        ttl: 7200
      })
      
      const blob = new Blob([payload], { type: 'application/json' })
      navigator.sendBeacon(`${this.baseURL}/api/cache/save`, blob)
      console.log('页面卸载前已保存数据')
    }
  }

  /**
   * 恢复数据并显示提示
   * @param experimentType - 实验类型
   * @param setDataCallback - 设置数据的回调函数
   * @returns 是否成功恢复数据
   */
  async restoreDataWithPrompt(experimentType: string, setDataCallback: (data: any) => void): Promise<boolean> {
    try {
      const cachedData = await this.getExperimentData(experimentType)
      
      if (cachedData && Object.keys(cachedData).length > 0) {
        const shouldRestore = confirm(
          '检测到您之前有未完成的实验数据，是否恢复？\n' +
          '点击"确定"恢复数据，点击"取消"开始新的实验。'
        )
        
        if (shouldRestore) {
          setDataCallback(cachedData)
          console.log('已恢复缓存的实验数据')
          return true
        } else {
          // 用户选择不恢复，删除缓存数据
          await this.deleteExperimentData(experimentType)
        }
      }
      
      return false
    } catch (error) {
      console.error('恢复数据失败:', error)
      return false
    }
  }
}

// 创建单例实例
const cacheService = new CacheService()

export default cacheService
export type { CacheResponse, CacheInfo }
