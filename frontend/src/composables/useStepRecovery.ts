/**
 * 步骤恢复管理组合式函数
 * 提供通用的步骤卡住检测和恢复机制
 */

import { ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

export interface StepRecoveryOptions {
  // 步骤卡住检测配置
  stuckDetection?: {
    enabled: boolean
    timeoutMs: number // 在同一步骤停留多长时间算卡住
    retryAttempts: number // 尝试次数
  }
  
  // 验证配置
  validation?: {
    allowSkip: boolean // 是否允许跳过验证
    skipWarning: string // 跳过验证的警告信息
  }
  
  // 调试配置
  debug?: {
    enabled: boolean
    logStepChanges: boolean
  }
}

export interface StepInfo {
  step: number
  substep?: number
  title: string
  description?: string
  hasValidation: boolean
  validated: boolean
}

export function useStepRecovery(options: StepRecoveryOptions = {}) {
  // 默认配置
  const defaultOptions: StepRecoveryOptions = {
    stuckDetection: {
      enabled: true,
      timeoutMs: 300000, // 5分钟
      retryAttempts: 3
    },
    validation: {
      allowSkip: true,
      skipWarning: '跳过验证可能影响实验结果的准确性，确定要跳过吗？'
    },
    debug: {
      enabled: false,
      logStepChanges: true
    }
  }
  
  const config = { ...defaultOptions, ...options }
  
  // 状态管理
  const showRecoveryDialog = ref(false)
  const currentStepInfo = ref<StepInfo>({
    step: 1,
    substep: undefined,
    title: '',
    description: '',
    hasValidation: false,
    validated: true
  })
  
  const stuckDetectionTimer = ref<number | null>(null)
  const stepHistory = ref<StepInfo[]>([])
  const retryCount = ref(0)
  const lastStepChangeTime = ref(Date.now())
  
  // 计算属性
  const isStuck = computed(() => {
    if (!config.stuckDetection?.enabled) return false
    
    const timeSinceLastChange = Date.now() - lastStepChangeTime.value
    return timeSinceLastChange > (config.stuckDetection?.timeoutMs || 300000)
  })
  
  const canSkipValidation = computed(() => {
    return config.validation?.allowSkip && currentStepInfo.value.hasValidation
  })
  
  const debugInfo = computed(() => ({
    currentStep: currentStepInfo.value,
    isStuck: isStuck.value,
    retryCount: retryCount.value,
    stepHistory: stepHistory.value.slice(-5), // 最近5步
    lastStepChangeTime: new Date(lastStepChangeTime.value).toLocaleString(),
    config
  }))
  
  // 更新步骤信息
  const updateStepInfo = (stepInfo: Partial<StepInfo>) => {
    const oldStep = currentStepInfo.value.step
    const oldSubstep = currentStepInfo.value.substep
    
    currentStepInfo.value = { ...currentStepInfo.value, ...stepInfo }
    
    // 检测步骤变化
    if (stepInfo.step !== undefined && (stepInfo.step !== oldStep || stepInfo.substep !== oldSubstep)) {
      onStepChange()
    }
    
    if (config.debug?.logStepChanges) {
      console.log('步骤更新:', currentStepInfo.value)
    }
  }
  
  // 步骤变化处理
  const onStepChange = () => {
    lastStepChangeTime.value = Date.now()
    retryCount.value = 0
    
    // 记录步骤历史
    stepHistory.value.push({ ...currentStepInfo.value })
    if (stepHistory.value.length > 20) {
      stepHistory.value = stepHistory.value.slice(-20)
    }
    
    // 重置卡住检测定时器
    resetStuckDetectionTimer()
  }
  
  // 重置卡住检测定时器
  const resetStuckDetectionTimer = () => {
    if (stuckDetectionTimer.value) {
      clearTimeout(stuckDetectionTimer.value)
    }
    
    if (config.stuckDetection?.enabled) {
      stuckDetectionTimer.value = setTimeout(() => {
        if (currentStepInfo.value.hasValidation && !currentStepInfo.value.validated) {
          checkIfStuck()
        }
      }, config.stuckDetection.timeoutMs)
    }
  }
  
  // 检查是否卡住
  const checkIfStuck = () => {
    retryCount.value++
    
    if (retryCount.value >= (config.stuckDetection?.retryAttempts || 3)) {
      showStuckDialog()
    } else {
      ElMessage.warning(`检测到步骤可能卡住，这是第 ${retryCount.value} 次提醒`)
      resetStuckDetectionTimer()
    }
  }
  
  // 显示卡住对话框
  const showStuckDialog = () => {
    showRecoveryDialog.value = true
  }
  
  // 恢复操作
  const resetCurrentStep = () => {
    ElMessage.success('步骤已重置')
    retryCount.value = 0
    lastStepChangeTime.value = Date.now()
    resetStuckDetectionTimer()
    return Promise.resolve()
  }
  
  const skipValidation = async () => {
    if (!canSkipValidation.value) {
      ElMessage.error('当前步骤不允许跳过验证')
      return Promise.reject('不允许跳过验证')
    }
    
    try {
      await ElMessageBox.confirm(
        config.validation?.skipWarning || '确定要跳过验证吗？',
        '跳过验证确认',
        {
          confirmButtonText: '确定跳过',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
      
      currentStepInfo.value.validated = true
      ElMessage.warning('已跳过当前步骤验证')
      onStepChange()
      return Promise.resolve()
    } catch {
      return Promise.reject('用户取消')
    }
  }
  
  const goToPreviousStep = () => {
    if (stepHistory.value.length > 1) {
      const previousStep = stepHistory.value[stepHistory.value.length - 2]
      updateStepInfo(previousStep)
      ElMessage.info('已返回上一步')
    } else {
      ElMessage.warning('已经是第一步了')
    }
    return Promise.resolve()
  }
  
  const showHelp = () => {
    // 这里可以根据当前步骤显示相应的帮助信息
    ElMessageBox.alert(
      currentStepInfo.value.description || '暂无帮助信息',
      `${currentStepInfo.value.title} - 帮助`,
      {
        confirmButtonText: '知道了'
      }
    )
    return Promise.resolve()
  }
  
  // 监听验证状态变化
  watch(
    () => currentStepInfo.value.validated,
    (newValidated) => {
      if (newValidated && stuckDetectionTimer.value) {
        clearTimeout(stuckDetectionTimer.value)
        stuckDetectionTimer.value = null
      } else if (!newValidated && currentStepInfo.value.hasValidation) {
        resetStuckDetectionTimer()
      }
    }
  )
  
  // 清理定时器
  const cleanup = () => {
    if (stuckDetectionTimer.value) {
      clearTimeout(stuckDetectionTimer.value)
      stuckDetectionTimer.value = null
    }
  }
  
  return {
    // 状态
    showRecoveryDialog,
    currentStepInfo,
    isStuck,
    canSkipValidation,
    debugInfo,
    
    // 方法
    updateStepInfo,
    resetCurrentStep,
    skipValidation,
    goToPreviousStep,
    showHelp,
    showStuckDialog,
    cleanup
  }
}
