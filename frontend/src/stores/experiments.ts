/**
 * 实验状态管理
 */

import { defineStore } from 'pinia'
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { experimentsApi } from '@/api/experiments'
import type { ExperimentType, ExperimentRecord } from '@/types'

export const useExperimentsStore = defineStore('experiments', () => {
  // 状态
  const experimentTypes = ref<ExperimentType[]>([])
  const currentExperiment = ref<ExperimentType | null>(null)
  const experimentRecords = ref<ExperimentRecord[]>([])
  const loading = ref(false)

  // 获取所有实验类型
  const fetchExperimentTypes = async () => {
    loading.value = true
    try {
      const types = await experimentsApi.getExperimentTypes()
      experimentTypes.value = types
      return types
    } catch (error) {
      ElMessage.error('获取实验类型失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取特定实验类型
  const fetchExperimentType = async (experimentCode: string) => {
    loading.value = true
    try {
      const experiment = await experimentsApi.getExperimentType(experimentCode)
      currentExperiment.value = experiment
      return experiment
    } catch (error) {
      ElMessage.error('获取实验信息失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 提交实验
  const submitExperiment = async (experimentCode: string, data: Record<string, any>) => {
    loading.value = true
    try {
      const response = await experimentsApi.submitExperiment({
        experiment_type_code: experimentCode,
        submission_data: data
      })
      
      ElMessage.success('实验提交成功')
      
      // 刷新实验记录
      await fetchExperimentRecords(experimentCode)
      
      return response
    } catch (error) {
      ElMessage.error('实验提交失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取实验记录
  const fetchExperimentRecords = async (experimentCode?: string) => {
    loading.value = true
    try {
      const response = await experimentsApi.getExperimentRecords(experimentCode)
      experimentRecords.value = response.records
      return response
    } catch (error) {
      ElMessage.error('获取实验记录失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 生成图形
  const generatePlot = async (experimentCode: string, data: Record<string, any>) => {
    loading.value = true
    try {
      const response = await experimentsApi.generatePlot({
        experiment_code: experimentCode,
        data
      })
      return response
    } catch (error) {
      ElMessage.error('生成图形失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 分析实验数据
  const analyzeExperiment = async (experimentCode: string, data: Record<string, any>) => {
    loading.value = true
    try {
      const response = await experimentsApi.analyzeExperiment({
        experiment_code: experimentCode,
        data
      })
      return response
    } catch (error) {
      ElMessage.error('分析实验数据失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 清除当前实验
  const clearCurrentExperiment = () => {
    currentExperiment.value = null
  }

  return {
    // 状态
    experimentTypes,
    currentExperiment,
    experimentRecords,
    loading,
    
    // 方法
    fetchExperimentTypes,
    fetchExperimentType,
    submitExperiment,
    fetchExperimentRecords,
    generatePlot,
    analyzeExperiment,
    clearCurrentExperiment
  }
})
