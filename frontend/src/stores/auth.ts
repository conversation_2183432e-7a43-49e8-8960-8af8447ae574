/**
 * 认证状态管理
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { authApi } from '@/api/auth'
import type { User, LoginRequest } from '@/types'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const user = ref<User | null>(null)
  const token = ref<string | null>(localStorage.getItem('access_token'))
  const loading = ref(false)

  // 计算属性
  const isAuthenticated = computed(() => !!token.value && !!user.value)
  const isStudent = computed(() => user.value?.user_type === 'student')
  const isAdmin = computed(() => user.value?.user_type === 'admin')

  // 初始化用户信息
  const initUser = async () => {
    if (token.value && !user.value) {
      try {
        const userInfo = await authApi.getCurrentUser()
        user.value = userInfo
      } catch (error) {
        // 如果获取用户信息失败，清除token
        logout()
      }
    }
  }

  // 登录
  const login = async (loginData: LoginRequest) => {
    loading.value = true
    try {
      const response = await authApi.login(loginData)
      
      // 保存token和用户信息
      token.value = response.access_token
      user.value = response.user_info
      
      localStorage.setItem('access_token', response.access_token)
      localStorage.setItem('user_info', JSON.stringify(response.user_info))
      
      ElMessage.success('登录成功')
      return response
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  // 登出
  const logout = async () => {
    try {
      if (token.value) {
        await authApi.logout()
      }
    } catch (error) {
      console.error('登出请求失败:', error)
    } finally {
      // 清除本地存储
      token.value = null
      user.value = null
      localStorage.removeItem('access_token')
      localStorage.removeItem('user_info')
      
      ElMessage.success('已退出登录')
    }
  }

  // 更新用户信息
  const updateUser = (newUserInfo: Partial<User>) => {
    if (user.value) {
      user.value = { ...user.value, ...newUserInfo }
      localStorage.setItem('user_info', JSON.stringify(user.value))
    }
  }

  return {
    // 状态
    user,
    token,
    loading,
    
    // 计算属性
    isAuthenticated,
    isStudent,
    isAdmin,
    
    // 方法
    initUser,
    login,
    logout,
    updateUser
  }
})
