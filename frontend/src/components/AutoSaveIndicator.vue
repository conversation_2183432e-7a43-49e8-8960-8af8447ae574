<template>
  <div class="auto-save-indicator" :class="{ 'saving': isSaving, 'error': hasError }">
    <div class="indicator-content">
      <el-icon class="indicator-icon">
        <Loading v-if="isSaving" />
        <CircleCheck v-else-if="!hasError" />
        <CircleClose v-else />
      </el-icon>
      <span class="indicator-text">{{ statusText }}</span>
      <span class="last-save-time" v-if="lastSaveTime">
        {{ formatTime(lastSaveTime) }}
      </span>
    </div>
    
    <!-- 缓存信息弹窗 -->
    <el-popover
      placement="bottom"
      :width="300"
      trigger="hover"
      v-if="cacheInfo"
    >
      <template #reference>
        <el-button 
          type="text" 
          size="small" 
          class="cache-info-btn"
          @click="showCacheInfo"
        >
          <el-icon><InfoFilled /></el-icon>
        </el-button>
      </template>
      
      <div class="cache-info">
        <h4>缓存信息</h4>
        <p><strong>会话ID:</strong> {{ cacheInfo.session_id?.substring(0, 8) }}...</p>
        <p><strong>缓存实验:</strong> {{ cacheInfo.total_count }} 个</p>
        <div v-if="cacheInfo.cached_experiments?.length > 0">
          <p><strong>实验列表:</strong></p>
          <ul>
            <li v-for="exp in cacheInfo.cached_experiments" :key="exp.experiment_type">
              {{ exp.experiment_type }} - {{ formatTime(exp.timestamp) }}
            </li>
          </ul>
        </div>
      </div>
    </el-popover>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Loading, CircleCheck, CircleClose, InfoFilled } from '@element-plus/icons-vue'
import cacheService from '@/services/cacheService'

interface Props {
  enabled?: boolean
  experimentType?: string
}

const props = withDefaults(defineProps<Props>(), {
  enabled: true,
  experimentType: 'current_control_circuit'
})

// 状态管理
const isSaving = ref(false)
const hasError = ref(false)
const lastSaveTime = ref<Date | null>(null)
const cacheInfo = ref<any>(null)

// 状态文本
const statusText = computed(() => {
  if (!props.enabled) return '自动保存已禁用'
  if (isSaving.value) return '正在保存...'
  if (hasError.value) return '保存失败'
  if (lastSaveTime.value) return '已保存'
  return '等待保存'
})

// 格式化时间
const formatTime = (time: string | Date) => {
  const date = typeof time === 'string' ? new Date(time) : time
  return date.toLocaleTimeString('zh-CN', { 
    hour: '2-digit', 
    minute: '2-digit',
    second: '2-digit'
  })
}

// 更新保存状态
const updateSaveStatus = (saving: boolean, error: boolean = false) => {
  isSaving.value = saving
  hasError.value = error
  
  if (!saving && !error) {
    lastSaveTime.value = new Date()
  }
}

// 获取缓存信息
const loadCacheInfo = async () => {
  try {
    cacheInfo.value = await cacheService.getCacheInfo()
  } catch (error) {
    console.error('获取缓存信息失败:', error)
  }
}

// 显示缓存信息
const showCacheInfo = async () => {
  await loadCacheInfo()
}

// 监听缓存服务事件（如果缓存服务支持事件）
let saveInterval: number | null = null

onMounted(() => {
  // 定期更新缓存信息
  saveInterval = window.setInterval(loadCacheInfo, 60000) // 每分钟更新一次
  loadCacheInfo()
  
  // 模拟保存状态更新（实际应该从缓存服务获取）
  const originalSave = cacheService.manualSave.bind(cacheService)
  cacheService.manualSave = async (experimentType: string, data: any) => {
    updateSaveStatus(true)
    try {
      const result = await originalSave(experimentType, data)
      updateSaveStatus(false, false)
      return result
    } catch (error) {
      updateSaveStatus(false, true)
      throw error
    }
  }
})

onUnmounted(() => {
  if (saveInterval) {
    clearInterval(saveInterval)
  }
})

// 暴露方法给父组件
defineExpose({
  updateSaveStatus,
  loadCacheInfo
})
</script>

<style scoped>
.auto-save-indicator {
  position: fixed;
  top: 20px;
  right: 20px;
  background: white;
  border-radius: 8px;
  padding: 8px 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
  z-index: 1000;
  transition: all 0.3s ease;
}

.auto-save-indicator.saving {
  border-color: #409eff;
  background: #f0f9ff;
}

.auto-save-indicator.error {
  border-color: #f56c6c;
  background: #fef0f0;
}

.indicator-content {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
}

.indicator-icon {
  font-size: 14px;
}

.indicator-text {
  color: #666;
  font-weight: 500;
}

.last-save-time {
  color: #999;
  font-size: 11px;
}

.cache-info-btn {
  padding: 0;
  margin-left: 4px;
  color: #999;
}

.cache-info h4 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 14px;
}

.cache-info p {
  margin: 4px 0;
  font-size: 12px;
  color: #666;
}

.cache-info ul {
  margin: 4px 0;
  padding-left: 16px;
}

.cache-info li {
  font-size: 11px;
  color: #999;
  margin: 2px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .auto-save-indicator {
    top: 10px;
    right: 10px;
    padding: 6px 8px;
  }
  
  .indicator-content {
    font-size: 11px;
  }
  
  .last-save-time {
    display: none;
  }
}
</style>
