<template>
  <el-dialog
    v-model="dialogVisible"
    title="提交实验"
    width="500px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <div class="submission-form">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
        label-position="left"
      >
        <el-form-item label="学号" prop="studentId">
          <el-input
            v-model="formData.studentId"
            placeholder="请输入学号"
            :disabled="loading"
          />
        </el-form-item>
        
        <el-form-item label="姓名" prop="studentName">
          <el-input
            v-model="formData.studentName"
            placeholder="请输入姓名"
            :disabled="loading"
          />
        </el-form-item>
        
        <el-form-item label="实验名称">
          <el-input
            :value="experimentName"
            disabled
          />
        </el-form-item>
        
        <el-form-item label="提交时间">
          <el-input
            :value="currentTime"
            disabled
          />
        </el-form-item>
      </el-form>
      
      <!-- 实验数据预览 -->
      <div v-if="experimentData" class="data-preview">
        <h4>实验数据预览</h4>
        <el-descriptions :column="2" border size="small">
          <el-descriptions-item
            v-for="(value, key) in experimentDataPreview"
            :key="key"
            :label="formatDataLabel(key)"
          >
            {{ formatDataValue(value) }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
      
      <!-- 状态显示 -->
      <div v-if="statusMessage" class="status-message">
        <el-alert
          :title="statusMessage"
          :type="statusType"
          :closable="false"
          show-icon
        />
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose" :disabled="loading">
          取消
        </el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="loading"
          :disabled="!canSubmit"
        >
          {{ loading ? '提交中...' : '确认提交' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { useAuthStore } from '@/stores/auth'

interface Props {
  visible: boolean
  experimentName: string
  experimentData?: Record<string, any>
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:visible': [visible: boolean]
  submit: [data: { studentId: string; studentName: string; experimentData: Record<string, any> }]
  close: []
}>()

// 响应式数据
const dialogVisible = ref(false)
const formRef = ref<FormInstance>()
const loading = ref(false)
const statusMessage = ref('')
const statusType = ref<'success' | 'warning' | 'info' | 'error'>('info')

// 表单数据
const formData = ref({
  studentId: '',
  studentName: ''
})

// 表单验证规则
const formRules: FormRules = {
  studentId: [
    { required: true, message: '请输入学号', trigger: 'blur' },
    { min: 6, max: 20, message: '学号长度应在6-20位之间', trigger: 'blur' }
  ],
  studentName: [
    { required: true, message: '请输入姓名', trigger: 'blur' },
    { min: 2, max: 10, message: '姓名长度应在2-10位之间', trigger: 'blur' }
  ]
}

// 使用认证store
const authStore = useAuthStore()

// 计算属性
const currentTime = computed(() => {
  return new Date().toLocaleString('zh-CN')
})

const canSubmit = computed(() => {
  return formData.value.studentId && formData.value.studentName && !loading.value
})

const experimentDataPreview = computed(() => {
  if (!props.experimentData) return {}
  
  // 只显示前几个关键数据项
  const preview: Record<string, any> = {}
  const keys = Object.keys(props.experimentData).slice(0, 6)
  
  keys.forEach(key => {
    preview[key] = props.experimentData![key]
  })
  
  return preview
})

// 监听visible变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  
  if (newVal) {
    // 如果用户已登录，自动填充信息
    if (authStore.isStudent && authStore.user) {
      const userInfo = authStore.user.additional_info
      if (userInfo) {
        formData.value.studentId = userInfo.student_id || ''
        formData.value.studentName = authStore.user.name || ''
      }
    }
    
    // 清除状态
    statusMessage.value = ''
    loading.value = false
  }
})

// 监听dialogVisible变化
watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
})

// 格式化数据标签
const formatDataLabel = (key: string) => {
  const labelMap: Record<string, string> = {
    k1_current: 'k=1电流数据',
    k01_current: 'k=0.1电流数据',
    plot_data: '图形数据',
    analysis_result: '分析结果'
  }
  
  return labelMap[key] || key
}

// 格式化数据值
const formatDataValue = (value: any) => {
  if (Array.isArray(value)) {
    return `${value.length}个数据点`
  } else if (typeof value === 'string' && value.length > 50) {
    return `${value.substring(0, 50)}...`
  } else if (typeof value === 'object') {
    return '对象数据'
  }
  
  return String(value)
}

// 处理提交
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    loading.value = true
    statusMessage.value = '正在提交实验数据...'
    statusType.value = 'info'
    
    // 发送提交事件
    emit('submit', {
      studentId: formData.value.studentId,
      studentName: formData.value.studentName,
      experimentData: props.experimentData || {}
    })
    
  } catch (error) {
    ElMessage.error('请检查表单填写是否正确')
  }
}

// 处理关闭
const handleClose = () => {
  if (loading.value) {
    ElMessage.warning('正在提交中，请稍候...')
    return
  }
  
  dialogVisible.value = false
  emit('close')
}

// 显示状态消息
const showStatus = (message: string, type: 'success' | 'warning' | 'info' | 'error') => {
  statusMessage.value = message
  statusType.value = type
}

// 显示成功状态
const showSuccess = (message: string) => {
  loading.value = false
  showStatus(message, 'success')
  
  setTimeout(() => {
    dialogVisible.value = false
    emit('close')
  }, 2000)
}

// 显示错误状态
const showError = (message: string) => {
  loading.value = false
  showStatus(message, 'error')
}

// 暴露方法给父组件
defineExpose({
  showStatus,
  showSuccess,
  showError
})
</script>

<style scoped>
.submission-form {
  padding: 10px 0;
}

.data-preview {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;
}

.data-preview h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 14px;
}

.status-message {
  margin-top: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
