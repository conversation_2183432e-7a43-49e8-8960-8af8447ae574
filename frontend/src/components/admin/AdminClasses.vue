<template>
  <div class="admin-classes">
    <div class="classes-header">
      <h2>班级管理</h2>
      <p>管理班级信息和分组</p>
    </div>
    
    <div class="classes-content">
      <el-table :data="classes" style="width: 100%">
        <el-table-column prop="name" label="班级名称" />
        <el-table-column prop="code" label="班级代码" width="120" />
        <el-table-column prop="department" label="院系" width="120" />
        <el-table-column prop="grade" label="年级" width="80" />
        <el-table-column prop="student_count" label="学生数量" width="100" />
        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <el-button size="small" @click="editClass(row)">编辑</el-button>
            <el-button size="small" type="primary" @click="manageGroups(row)">分组管理</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import type { Class } from '@/types'
import { adminApi } from '@/api/admin'

const classes = ref<Class[]>([])

const fetchClasses = async () => {
  try {
    const res = await adminApi.getClasses()
    classes.value = (res as any) as Class[]
  } catch (error) {
    ElMessage.error('获取班级列表失败')
  }
}

const editClass = (cls: Class) => {
  ElMessage.info('编辑功能开发中')
}

const manageGroups = (cls: Class) => {
  ElMessage.info('分组管理功能开发中')
}

onMounted(() => {
  fetchClasses()
})
</script>

<style scoped>
.admin-classes {
  padding: 0;
}

.classes-header {
  margin-bottom: 20px;
}

.classes-header h2 {
  margin: 0 0 5px 0;
  color: #303133;
}

.classes-header p {
  margin: 0;
  color: #606266;
}

.classes-content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
}
</style>
