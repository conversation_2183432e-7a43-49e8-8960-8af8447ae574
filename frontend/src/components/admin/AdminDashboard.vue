<template>
  <div class="admin-dashboard">
    <div class="dashboard-header">
      <h2>仪表盘</h2>
      <p>系统概览和统计信息</p>
    </div>
    
    <!-- 统计卡片 -->
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon">
          <el-icon size="32" color="#409eff"><User /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ stats.totalStudents }}</div>
          <div class="stat-label">学生总数</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon">
          <el-icon size="32" color="#67c23a"><Histogram /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ stats.totalExperiments }}</div>
          <div class="stat-label">实验总数</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon">
          <el-icon size="32" color="#e6a23c"><Document /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ stats.totalSubmissions }}</div>
          <div class="stat-label">提交总数</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon">
          <el-icon size="32" color="#f56c6c"><TrendCharts /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ stats.passRate }}%</div>
          <div class="stat-label">通过率</div>
        </div>
      </div>
    </div>
    
    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-row">
      <el-col :span="12">
        <div class="chart-card">
          <h3>实验提交趋势</h3>
          <div class="chart-placeholder">
            <el-empty description="图表功能开发中" />
          </div>
        </div>
      </el-col>
      
      <el-col :span="12">
        <div class="chart-card">
          <h3>实验通过率分布</h3>
          <div class="chart-placeholder">
            <el-empty description="图表功能开发中" />
          </div>
        </div>
      </el-col>
    </el-row>
    
    <!-- 最近活动 -->
    <div class="recent-activities">
      <h3>最近活动</h3>
      <el-table :data="recentActivities" style="width: 100%">
        <el-table-column prop="time" label="时间" width="180" />
        <el-table-column prop="student" label="学生" width="120" />
        <el-table-column prop="experiment" label="实验" width="200" />
        <el-table-column prop="action" label="操作" width="120" />
        <el-table-column prop="status" label="状态">
          <template #default="{ row }">
            <el-tag
              :type="row.status === 'passed' ? 'success' : row.status === 'failed' ? 'danger' : 'warning'"
              size="small"
            >
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { User, Histogram, Document, TrendCharts } from '@element-plus/icons-vue'
import { adminApi } from '@/api/admin'

// 响应式数据
const stats = ref({
  totalStudents: 0,
  totalExperiments: 0,
  totalSubmissions: 0,
  passRate: 0
})

const recentActivities = ref<Array<{ time: string, student: string, experiment: string, action: string, status: string }>>([])

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    passed: '通过',
    failed: '未通过',
    pending: '待审核',
    approved: '通过',
    rejected: '未通过',
    submitted: '已提交',
    reviewed: '已评审',
  }
  return statusMap[status] || status
}

// 获取统计数据
const fetchStats = async () => {
  try {
    const res = await adminApi.getOverviewStats()
    const data: any = res
    stats.value = {
      totalStudents: data.total_students ?? 0,
      totalExperiments: data.total_experiments ?? 0,
      totalSubmissions: data.total_submissions ?? 0,
      passRate: data.pass_rate ?? 0,
    }

    const activities = (data.recent_activities || []) as any[]
    recentActivities.value = activities.map(a => ({
      time: a.submitted_at || '',
      student: a.student_name || a.student_no || '—',
      experiment: a.experiment_name || a.experiment_code || '—',
      action: '提交实验',
      status: a.status || (a.is_passed === true ? 'approved' : a.is_passed === false ? 'rejected' : 'submitted'),
    }))
  } catch (e) {
    ElMessage.error('获取统计数据失败')
  }
}

// 组件挂载时获取数据
onMounted(() => {
  fetchStats()
})
</script>

<style scoped>
.admin-dashboard {
  padding: 0;
}

.dashboard-header {
  margin-bottom: 30px;
}

.dashboard-header h2 {
  margin: 0 0 5px 0;
  color: #303133;
}

.dashboard-header p {
  margin: 0;
  color: #606266;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 15px;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 2rem;
  font-weight: 600;
  color: #303133;
  margin-bottom: 5px;
}

.stat-label {
  color: #606266;
  font-size: 0.9rem;
}

.charts-row {
  margin-bottom: 30px;
}

.chart-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chart-card h3 {
  margin: 0 0 20px 0;
  color: #303133;
}

.chart-placeholder {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.recent-activities {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.recent-activities h3 {
  margin: 0 0 20px 0;
  color: #303133;
}
</style>
