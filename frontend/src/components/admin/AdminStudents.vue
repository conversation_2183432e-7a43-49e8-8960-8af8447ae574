<template>
  <div class="admin-students">
    <div class="students-header">
      <h2>学生管理</h2>
      <div class="header-actions">
        <el-button type="primary" @click="showAddDialog = true">
          <el-icon><Plus /></el-icon>
          添加学生
        </el-button>
      </div>
    </div>
    
    <!-- 搜索和筛选 -->
    <div class="search-bar">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-input
            v-model="searchQuery"
            placeholder="搜索学生姓名或学号"
            :prefix-icon="Search"
            clearable
            @input="handleSearch"
          />
        </el-col>
        <el-col :span="4">
          <el-select v-model="classFilter" placeholder="选择班级" clearable>
            <el-option
              v-for="cls in classes"
              :key="cls.id"
              :label="cls.name"
              :value="cls.id"
            />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select v-model="statusFilter" placeholder="选择状态" clearable>
            <el-option label="激活" value="active" />
            <el-option label="禁用" value="inactive" />
          </el-select>
        </el-col>
      </el-row>
    </div>
    
    <!-- 学生列表 -->
    <div class="students-table">
      <el-table
        :data="filteredStudents"
        v-loading="loading"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="student_id" label="学号" width="120" />
        <el-table-column prop="name" label="姓名" width="100" />
        <el-table-column prop="class_name" label="班级" width="120" />
        <el-table-column prop="group_name" label="分组" width="100" />
        <el-table-column prop="email" label="邮箱" />
        <el-table-column prop="is_active" label="状态" width="80">
          <template #default="{ row }">
            <el-tag :type="row.is_active ? 'success' : 'danger'" size="small">
              {{ row.is_active ? '激活' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <el-button size="small" @click="editStudent(row)">编辑</el-button>
            <el-button
              size="small"
              :type="row.is_active ? 'warning' : 'success'"
              @click="toggleStudentStatus(row)"
            >
              {{ row.is_active ? '禁用' : '启用' }}
            </el-button>
            <el-button size="small" type="danger" @click="deleteStudent(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="totalStudents"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
    
    <!-- 添加/编辑学生对话框 -->
    <el-dialog
      v-model="showAddDialog"
      :title="editingStudent ? '编辑学生' : '添加学生'"
      width="500px"
    >
      <el-form
        ref="studentFormRef"
        :model="studentForm"
        :rules="studentRules"
        label-width="80px"
      >
        <el-form-item label="学号" prop="student_id">
          <el-input v-model="studentForm.student_id" placeholder="请输入学号" />
        </el-form-item>
        <el-form-item label="姓名" prop="name">
          <el-input v-model="studentForm.name" placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="班级" prop="class_id">
          <el-select v-model="studentForm.class_id" placeholder="选择班级">
            <el-option
              v-for="cls in classes"
              :key="cls.id"
              :label="cls.name"
              :value="cls.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="studentForm.email" placeholder="请输入邮箱" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showAddDialog = false">取消</el-button>
        <el-button type="primary" @click="saveStudent" :loading="saving">
          {{ saving ? '保存中...' : '保存' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { Plus, Search } from '@element-plus/icons-vue'
import type { Student, Class } from '@/types'
import { adminApi } from '@/api/admin'

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const showAddDialog = ref(false)
const editingStudent = ref<Student | null>(null)
const searchQuery = ref('')
const classFilter = ref<number | null>(null)
const statusFilter = ref<string | null>(null)
const currentPage = ref(1)
const pageSize = ref(20)
const totalStudents = ref(0)
const selectedStudents = ref<Student[]>([])

const students = ref<Student[]>([])
const classes = ref<Class[]>([])

const studentFormRef = ref<FormInstance>()
const studentForm = ref({
  student_id: '',
  name: '',
  class_id: null as number | null,
  email: ''
})

// 表单验证规则
const studentRules: FormRules = {
  student_id: [
    { required: true, message: '请输入学号', trigger: 'blur' },
    { min: 6, max: 20, message: '学号长度应在6-20位之间', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' },
    { min: 2, max: 10, message: '姓名长度应在2-10位之间', trigger: 'blur' }
  ],
  class_id: [
    { required: true, message: '请选择班级', trigger: 'change' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ]
}

// 计算属性
const filteredStudents = computed(() => {
  let result = students.value

  if (searchQuery.value) {
    result = result.filter(student =>
      student.name.includes(searchQuery.value) ||
      student.student_id.includes(searchQuery.value)
    )
  }

  if (classFilter.value) {
    const selected = classes.value.find(c => c.id === classFilter.value)
    const selectedName = selected?.name
    if (selectedName) {
      result = result.filter(student => student.class_name === selectedName)
    }
  }

  if (statusFilter.value) {
    const isActive = statusFilter.value === 'active'
    result = result.filter(student => student.is_active === isActive)
  }

  return result
})

// 方法
const fetchStudents = async () => {
  loading.value = true
  try {
    const params: any = {}
    if (classFilter.value) params.class_id = classFilter.value
    const res = await adminApi.getStudents(params)
    const data: any = res
    students.value = (data as any) as Student[]
    totalStudents.value = students.value.length
  } catch (error) {
    ElMessage.error('获取学生列表失败')
  } finally {
    loading.value = false
  }
}

const fetchClasses = async () => {
  try {
    const res = await adminApi.getClasses()
    classes.value = (res as any) as Class[]
  } catch (error) {
    ElMessage.error('获取班级列表失败')
  }
}

const handleSearch = () => {
  // 搜索逻辑已在计算属性中实现
}

const handleSelectionChange = (selection: Student[]) => {
  selectedStudents.value = selection
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  fetchStudents()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  fetchStudents()
}

const editStudent = (student: Student) => {
  editingStudent.value = student
  studentForm.value = {
    student_id: student.student_id,
    name: student.name,
    class_id: null, // 需要根据class_name找到对应的class_id
    email: student.email || ''
  }
  showAddDialog.value = true
}

const saveStudent = async () => {
  if (!studentFormRef.value) return
  
  try {
    await studentFormRef.value.validate()
    
    saving.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success(editingStudent.value ? '学生信息更新成功' : '学生添加成功')
    showAddDialog.value = false
    resetForm()
    fetchStudents()
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

const toggleStudentStatus = async (student: Student) => {
  try {
    const action = student.is_active ? '禁用' : '启用'
    await ElMessageBox.confirm(`确定要${action}学生 ${student.name} 吗？`, '确认操作')
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    student.is_active = !student.is_active
    ElMessage.success(`${action}成功`)
  } catch (error) {
    // 用户取消操作
  }
}

const deleteStudent = async (student: Student) => {
  try {
    await ElMessageBox.confirm(`确定要删除学生 ${student.name} 吗？此操作不可恢复！`, '确认删除', {
      type: 'warning'
    })
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    const index = students.value.findIndex(s => s.id === student.id)
    if (index > -1) {
      students.value.splice(index, 1)
    }
    
    ElMessage.success('删除成功')
  } catch (error) {
    // 用户取消操作
  }
}

const resetForm = () => {
  editingStudent.value = null
  studentForm.value = {
    student_id: '',
    name: '',
    class_id: null,
    email: ''
  }
  studentFormRef.value?.resetFields()
}

// 组件挂载时获取数据
onMounted(() => {
  fetchStudents()
  fetchClasses()
})
</script>

<style scoped>
.admin-students {
  padding: 0;
}

.students-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.students-header h2 {
  margin: 0;
  color: #303133;
}

.search-bar {
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.students-table {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}
</style>
