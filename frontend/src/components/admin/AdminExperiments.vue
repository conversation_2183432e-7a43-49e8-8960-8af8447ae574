<template>
  <div class="admin-experiments">
    <div class="experiments-header">
      <h2>实验管理</h2>
      <p>管理实验类型和配置</p>
    </div>

    <div class="experiments-content">
      <el-table :data="experiments" style="width: 100%">
        <el-table-column prop="name" label="实验名称" />
        <el-table-column prop="code" label="实验代码" width="150" />
        <el-table-column prop="duration_minutes" label="预计时长(分钟)" width="120" />
        <el-table-column prop="max_score" label="满分" width="80" />
        <el-table-column prop="is_active" label="状态" width="80">
          <template #default="{ row }">
            <el-tag :type="row.is_active ? 'success' : 'danger'" size="small">
              {{ row.is_active ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150">
          <template #default="{ row }">
            <el-button size="small" @click="editExperiment(row)">编辑</el-button>
            <el-button size="small" type="primary" @click="viewStats(row)">统计</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import type { ExperimentType } from '@/types'
import { request } from '@/api/index'


const experiments = ref<ExperimentType[]>([])

const fetchExperiments = async () => {
  try {
    const res = await request.get('/api/experiments/types')
    experiments.value = (res as any) as ExperimentType[]
  } catch (error) {
    ElMessage.error('获取实验列表失败')
  }
}

const editExperiment = (experiment: ExperimentType) => {
  ElMessage.info('编辑功能开发中')
}

const viewStats = (experiment: ExperimentType) => {
  ElMessage.info('统计功能开发中')
}

onMounted(() => {
  fetchExperiments()
})
</script>

<style scoped>
.admin-experiments {
  padding: 0;
}

.experiments-header {
  margin-bottom: 20px;
}

.experiments-header h2 {
  margin: 0 0 5px 0;
  color: #303133;
}

.experiments-header p {
  margin: 0;
  color: #606266;
}

.experiments-content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
}
</style>
