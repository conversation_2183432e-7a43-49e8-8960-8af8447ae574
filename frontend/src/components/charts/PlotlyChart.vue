<template>
  <div class="plotly-chart-container">
    <div 
      ref="plotContainer" 
      class="plotly-chart"
      :style="{ width: `${width}px`, height: `${height}px` }"
    ></div>
    
    <!-- 操作按钮 -->
    <div v-if="showControls" class="chart-controls">
      <el-button size="small" @click="downloadPNG">
        <el-icon><Download /></el-icon>
        下载PNG
      </el-button>
      <el-button size="small" @click="downloadSVG">
        <el-icon><Download /></el-icon>
        下载SVG
      </el-button>
      <el-button size="small" @click="refreshChart">
        <el-icon><Refresh /></el-icon>
        刷新
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Download, Refresh } from '@element-plus/icons-vue'

// 动态导入plotly.js以减少初始包大小
let Plotly: any = null

interface Props {
  data: any[]
  layout: any
  config?: any
  width?: number
  height?: number
  showControls?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  config: () => ({}),
  width: 800,
  height: 600,
  showControls: true
})

const emit = defineEmits<{
  plotReady: [plotElement: any]
  plotError: [error: Error]
}>()

const plotContainer = ref<HTMLElement>()
let plotElement: any = null

// 加载Plotly.js
const loadPlotly = async () => {
  if (!Plotly) {
    try {
      // 动态导入plotly.js
      const plotlyModule = await import('plotly.js-dist-min')
      Plotly = plotlyModule.default || plotlyModule
    } catch (error) {
      console.error('加载Plotly失败:', error)
      ElMessage.error('图表库加载失败，请刷新页面重试')
      throw error
    }
  }
  return Plotly
}

// 创建图表
const createPlot = async () => {
  if (!plotContainer.value) return
  
  try {
    await loadPlotly()
    
    // 合并默认配置
    const defaultConfig = {
      responsive: true,
      displayModeBar: true,
      modeBarButtonsToRemove: ['pan2d', 'lasso2d', 'select2d'],
      displaylogo: false
    }
    
    const finalConfig = { ...defaultConfig, ...props.config }
    
    // 创建图表
    plotElement = await Plotly.newPlot(
      plotContainer.value,
      props.data,
      props.layout,
      finalConfig
    )
    
    emit('plotReady', plotElement)
  } catch (error) {
    console.error('创建图表失败:', error)
    emit('plotError', error as Error)
    ElMessage.error('创建图表失败')
  }
}

// 更新图表
const updatePlot = async () => {
  if (!plotElement || !Plotly) return
  
  try {
    await Plotly.react(plotContainer.value, props.data, props.layout, props.config)
  } catch (error) {
    console.error('更新图表失败:', error)
    ElMessage.error('更新图表失败')
  }
}

// 刷新图表
const refreshChart = () => {
  createPlot()
}

// 下载PNG
const downloadPNG = async () => {
  if (!plotElement || !Plotly) {
    ElMessage.warning('图表未准备就绪')
    return
  }
  
  try {
    const filename = `chart_${Date.now()}.png`
    await Plotly.downloadImage(plotContainer.value, {
      format: 'png',
      width: props.width,
      height: props.height,
      filename
    })
    ElMessage.success('PNG图片已下载')
  } catch (error) {
    console.error('下载PNG失败:', error)
    ElMessage.error('下载PNG失败')
  }
}

// 下载SVG
const downloadSVG = async () => {
  if (!plotElement || !Plotly) {
    ElMessage.warning('图表未准备就绪')
    return
  }
  
  try {
    const filename = `chart_${Date.now()}.svg`
    await Plotly.downloadImage(plotContainer.value, {
      format: 'svg',
      width: props.width,
      height: props.height,
      filename
    })
    ElMessage.success('SVG图片已下载')
  } catch (error) {
    console.error('下载SVG失败:', error)
    ElMessage.error('下载SVG失败')
  }
}

// 获取图表的PNG base64数据
const getImageData = async (): Promise<string> => {
  if (!plotElement || !Plotly) {
    throw new Error('图表未准备就绪')
  }
  
  try {
    const imageData = await Plotly.toImage(plotContainer.value, {
      format: 'png',
      width: props.width,
      height: props.height
    })
    
    // 移除data:image/png;base64,前缀
    return imageData.replace(/^data:image\/png;base64,/, '')
  } catch (error) {
    console.error('获取图片数据失败:', error)
    throw new Error('获取图片数据失败')
  }
}

// 监听数据变化
watch([() => props.data, () => props.layout], () => {
  if (plotElement) {
    updatePlot()
  }
}, { deep: true })

// 监听尺寸变化
watch([() => props.width, () => props.height], () => {
  if (plotElement && Plotly) {
    Plotly.relayout(plotContainer.value, {
      width: props.width,
      height: props.height
    })
  }
})

onMounted(async () => {
  await nextTick()
  createPlot()
})

onUnmounted(() => {
  if (plotElement && Plotly) {
    Plotly.purge(plotContainer.value)
  }
})

// 暴露方法给父组件
defineExpose({
  refreshChart,
  downloadPNG,
  downloadSVG,
  getImageData
})
</script>

<style scoped>
.plotly-chart-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.plotly-chart {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background: white;
}

.chart-controls {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  justify-content: center;
}

.chart-controls .el-button {
  min-width: 80px;
}
</style>
