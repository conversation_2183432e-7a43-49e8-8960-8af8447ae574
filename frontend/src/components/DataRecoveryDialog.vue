<template>
  <el-dialog
    v-model="visible"
    title="发现未完成的实验数据"
    width="500px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="false"
  >
    <div class="recovery-content">
      <div class="recovery-icon">
        <el-icon size="48" color="#409eff">
          <DocumentCopy />
        </el-icon>
      </div>
      
      <div class="recovery-message">
        <h3>检测到您之前有未完成的实验数据</h3>
        <p>我们发现您在 <strong>{{ formatTime(cacheTimestamp) }}</strong> 保存了实验数据。</p>
        
        <div class="data-preview" v-if="previewData">
          <h4>数据预览：</h4>
          <ul>
            <li>当前步骤: 第 {{ previewData.currentStep }} 步</li>
            <li>k=1 数据: {{ getDataCount(previewData.k1Data) }} 条</li>
            <li>k=0.1 数据: {{ getDataCount(previewData.k01Data) }} 条</li>
            <li v-if="previewData.studentInfo?.studentId">
              学生信息: {{ previewData.studentInfo.name }} ({{ previewData.studentInfo.studentId }})
            </li>
          </ul>
        </div>
        
        <div class="recovery-options">
          <p><strong>您希望如何处理这些数据？</strong></p>
          <div class="option-buttons">
            <el-button 
              type="primary" 
              @click="handleRestore"
              :loading="restoring"
            >
              <el-icon><Refresh /></el-icon>
              恢复数据并继续实验
            </el-button>
            
            <el-button 
              @click="handleStartNew"
              :loading="clearing"
            >
              <el-icon><DocumentAdd /></el-icon>
              开始新的实验
            </el-button>
          </div>
        </div>
        
        <div class="recovery-note">
          <el-alert
            title="提示"
            type="info"
            :closable="false"
            show-icon
          >
            <template #default>
              <p>恢复数据将帮助您从上次离开的地方继续实验，避免重复工作。</p>
              <p>选择"开始新的实验"将清除所有缓存数据。</p>
            </template>
          </el-alert>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { DocumentCopy, Refresh, DocumentAdd } from '@element-plus/icons-vue'

interface Props {
  modelValue: boolean
  cacheData?: any
  cacheTimestamp?: string
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'restore', data: any): void
  (e: 'startNew'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 状态管理
const restoring = ref(false)
const clearing = ref(false)

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const previewData = computed(() => props.cacheData)

// 格式化时间
const formatTime = (timestamp?: string) => {
  if (!timestamp) return '未知时间'
  
  const date = new Date(timestamp)
  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffMins = Math.floor(diffMs / (1000 * 60))
  const diffHours = Math.floor(diffMins / 60)
  const diffDays = Math.floor(diffHours / 24)
  
  if (diffMins < 1) return '刚刚'
  if (diffMins < 60) return `${diffMins} 分钟前`
  if (diffHours < 24) return `${diffHours} 小时前`
  if (diffDays < 7) return `${diffDays} 天前`
  
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 获取数据条数
const getDataCount = (data: any[]) => {
  if (!Array.isArray(data)) return 0
  return data.filter(item => item.current !== null && item.current !== undefined).length
}

// 处理恢复数据
const handleRestore = async () => {
  restoring.value = true
  try {
    emit('restore', props.cacheData)
    ElMessage.success('数据恢复成功，继续您的实验吧！')
    visible.value = false
  } catch (error) {
    console.error('恢复数据失败:', error)
    ElMessage.error('恢复数据失败，请重试')
  } finally {
    restoring.value = false
  }
}

// 处理开始新实验
const handleStartNew = async () => {
  clearing.value = true
  try {
    emit('startNew')
    ElMessage.info('已开始新的实验')
    visible.value = false
  } catch (error) {
    console.error('清除数据失败:', error)
    ElMessage.error('清除数据失败，请重试')
  } finally {
    clearing.value = false
  }
}
</script>

<style scoped>
.recovery-content {
  text-align: center;
  padding: 20px 0;
}

.recovery-icon {
  margin-bottom: 20px;
}

.recovery-message h3 {
  color: #333;
  margin-bottom: 12px;
  font-size: 18px;
  font-weight: 600;
}

.recovery-message p {
  color: #666;
  margin-bottom: 16px;
  line-height: 1.5;
}

.data-preview {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  margin: 16px 0;
  text-align: left;
}

.data-preview h4 {
  color: #333;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 600;
}

.data-preview ul {
  margin: 0;
  padding-left: 20px;
}

.data-preview li {
  color: #666;
  margin: 4px 0;
  font-size: 13px;
}

.recovery-options {
  margin: 20px 0;
}

.recovery-options p {
  font-weight: 600;
  margin-bottom: 16px;
}

.option-buttons {
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
}

.option-buttons .el-button {
  min-width: 160px;
}

.recovery-note {
  margin-top: 20px;
  text-align: left;
}

.recovery-note .el-alert {
  background: #f0f9ff;
  border: 1px solid #b3d8ff;
}

.recovery-note p {
  margin: 4px 0;
  font-size: 12px;
  color: #666;
}

/* 响应式设计 */
@media (max-width: 600px) {
  .option-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .option-buttons .el-button {
    width: 100%;
    max-width: 200px;
  }
}
</style>
