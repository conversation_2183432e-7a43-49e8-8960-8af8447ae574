<template>
  <div class="experiment-record-table-factory">
    <!-- 制流电路实验记录表 -->
    <CurrentControlCircuitRecordTable
      v-if="experimentType === 'current_control_circuit' && parsedData?.isValid"
      :data="parsedData.data"
      :record="record"
    />
    
    <!-- 示波器实验记录表 -->
    <OscilloscopeRecordTable
      v-else-if="experimentType === 'oscilloscope' && parsedData?.isValid"
      :data="parsedData.data"
      :record="record"
    />
    
    <!-- 通用实验记录表（用于未知类型或解析失败的情况） -->
    <GenericExperimentRecordTable
      v-else
      :record="record"
      :parsed-data="parsedData"
      :experiment-type="experimentType"
    />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import CurrentControlCircuitRecordTable from './CurrentControlCircuitRecordTable.vue'
import OscilloscopeRecordTable from './OscilloscopeRecordTable.vue'
import GenericExperimentRecordTable from './GenericExperimentRecordTable.vue'
import { parseExperimentData } from '@/services/experimentDataParser'
import type { ExperimentRecord, ParsedExperimentData } from '@/services/experimentDataParser'

interface Props {
  record: ExperimentRecord
  experimentType: string
}

const props = defineProps<Props>()

// 解析实验数据
const parsedData = computed<ParsedExperimentData | null>(() => {
  if (!props.record.submission_data) return null
  
  try {
    return parseExperimentData(props.experimentType, props.record.submission_data)
  } catch (error) {
    console.error('解析实验数据失败:', error)
    return null
  }
})
</script>

<style scoped>
.experiment-record-table-factory {
  width: 100%;
  height: 100%;
}
</style>
