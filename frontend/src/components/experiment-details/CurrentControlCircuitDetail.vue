<template>
  <div class="current-control-circuit-detail">
    <!-- 基本信息 -->
    <el-card class="detail-section" shadow="never">
      <template #header>
        <div class="section-header">
          <el-icon><User /></el-icon>
          <span>基本信息</span>
        </div>
      </template>
      
      <el-descriptions :column="3" border size="small">
        <el-descriptions-item label="学生姓名">
          {{ data.student_name || '未知' }}
        </el-descriptions-item>
        <el-descriptions-item label="学号">
          {{ data.student_id || '未知' }}
        </el-descriptions-item>
        <el-descriptions-item label="提交时间">
          {{ formatDateTime(record.submitted_at) }}
        </el-descriptions-item>
        <el-descriptions-item label="实验状态">
          <el-tag :type="record.is_passed ? 'success' : 'danger'" v-if="record.is_passed !== null">
            {{ record.is_passed ? '通过' : '未通过' }}
          </el-tag>
          <span v-else>待评估</span>
        </el-descriptions-item>
        <el-descriptions-item label="得分">
          {{ record.score !== null ? record.score : '未评分' }}
        </el-descriptions-item>
        <el-descriptions-item label="审核状态">
          <el-tag :type="getStatusType(record.status)" size="small">
            {{ getStatusText(record.status) }}
          </el-tag>
        </el-descriptions-item>
      </el-descriptions>
      
      <!-- 环境条件 -->
      <div v-if="data.temperature || data.humidity" class="environment-info">
        <h4>实验环境</h4>
        <el-descriptions :column="2" size="small">
          <el-descriptions-item v-if="data.temperature" label="温度">
            {{ data.temperature }}°C
          </el-descriptions-item>
          <el-descriptions-item v-if="data.humidity" label="湿度">
            {{ data.humidity }}%
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-card>

    <!-- 实验数据 -->
    <el-card class="detail-section" shadow="never">
      <template #header>
        <div class="section-header">
          <el-icon><DataAnalysis /></el-icon>
          <span>实验数据</span>
        </div>
      </template>
      
      <el-tabs v-model="activeTab" type="card">
        <!-- k=1数据 -->
        <el-tab-pane label="k=1电流数据" name="k1">
          <div class="data-table-container">
            <el-table :data="data.k1Data" border stripe size="small">
              <el-table-column prop="ratio" label="接入比例" width="120" align="center">
                <template #default="{ row }">
                  {{ row.ratio.toFixed(1) }}
                </template>
              </el-table-column>
              <el-table-column prop="current" label="电流值 (A)" width="120" align="center">
                <template #default="{ row }">
                  <span v-if="row.current !== null" :class="{ 'missing-data': row.current === null }">
                    {{ row.current?.toFixed(4) || '-' }}
                  </span>
                  <el-tag v-else type="warning" size="small">未测量</el-tag>
                </template>
              </el-table-column>
            </el-table>
            
            <!-- k=1统计信息 -->
            <div v-if="stats.k1Stats.count > 0" class="stats-info">
              <h5>统计信息</h5>
              <el-descriptions :column="5" size="small">
                <el-descriptions-item label="数据点数">{{ stats.k1Stats.count }}</el-descriptions-item>
                <el-descriptions-item label="最小值">{{ stats.k1Stats.min }} A</el-descriptions-item>
                <el-descriptions-item label="最大值">{{ stats.k1Stats.max }} A</el-descriptions-item>
                <el-descriptions-item label="平均值">{{ stats.k1Stats.average }} A</el-descriptions-item>
                <el-descriptions-item label="标准差">{{ stats.k1Stats.standardDeviation }} A</el-descriptions-item>
              </el-descriptions>
            </div>
          </div>
        </el-tab-pane>
        
        <!-- k=0.1数据 -->
        <el-tab-pane label="k=0.1电流数据" name="k01">
          <div class="data-table-container">
            <el-table :data="data.k01Data" border stripe size="small">
              <el-table-column prop="ratio" label="接入比例" width="120" align="center">
                <template #default="{ row }">
                  {{ row.ratio.toFixed(1) }}
                </template>
              </el-table-column>
              <el-table-column prop="current" label="电流值 (A)" width="120" align="center">
                <template #default="{ row }">
                  <span v-if="row.current !== null" :class="{ 'missing-data': row.current === null }">
                    {{ row.current?.toFixed(4) || '-' }}
                  </span>
                  <el-tag v-else type="warning" size="small">未测量</el-tag>
                </template>
              </el-table-column>
            </el-table>
            
            <!-- k=0.1统计信息 -->
            <div v-if="stats.k01Stats.count > 0" class="stats-info">
              <h5>统计信息</h5>
              <el-descriptions :column="5" size="small">
                <el-descriptions-item label="数据点数">{{ stats.k01Stats.count }}</el-descriptions-item>
                <el-descriptions-item label="最小值">{{ stats.k01Stats.min }} A</el-descriptions-item>
                <el-descriptions-item label="最大值">{{ stats.k01Stats.max }} A</el-descriptions-item>
                <el-descriptions-item label="平均值">{{ stats.k01Stats.average }} A</el-descriptions-item>
                <el-descriptions-item label="标准差">{{ stats.k01Stats.standardDeviation }} A</el-descriptions-item>
              </el-descriptions>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 实验图形 -->
    <el-card class="detail-section" shadow="never">
      <template #header>
        <div class="section-header">
          <el-icon><PictureRounded /></el-icon>
          <span>实验图形</span>
        </div>
      </template>

      <div class="plot-container">
        <PlotlyChart
          v-if="plotData.data"
          :data="plotData.data"
          :layout="plotData.layout"
          :config="plotData.config"
          :width="800"
          :height="600"
          @plot-ready="onPlotReady"
          @plot-error="onPlotError"
        />
        <div v-else class="no-plot">
          <el-empty description="暂无图形数据" />
        </div>
      </div>
    </el-card>

    <!-- AI分析结果 -->
    <el-card v-if="record.analysis_result" class="detail-section" shadow="never">
      <template #header>
        <div class="section-header">
          <el-icon><ChatDotRound /></el-icon>
          <span>AI分析结果</span>
        </div>
      </template>
      
      <div class="analysis-content">
        <el-alert
          :title="record.is_passed ? '实验通过' : '实验未通过'"
          :type="record.is_passed ? 'success' : 'warning'"
          :closable="false"
          show-icon
        />
        <div class="analysis-text">
          {{ record.analysis_result }}
        </div>
      </div>
    </el-card>

    <!-- 实验备注 -->
    <el-card v-if="data.notes" class="detail-section" shadow="never">
      <template #header>
        <div class="section-header">
          <el-icon><EditPen /></el-icon>
          <span>实验备注</span>
        </div>
      </template>
      
      <div class="notes-content">
        {{ data.notes }}
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import {
  User,
  DataAnalysis,
  PictureRounded,
  ChatDotRound,
  EditPen
} from '@element-plus/icons-vue'
import type {
  CurrentControlCircuitData,
  ExperimentRecord
} from '@/services/experimentDataParser'
import { calculateCurrentControlCircuitStats } from '@/services/experimentDataParser'
import PlotlyChart from '@/components/charts/PlotlyChart.vue'
import { generateCurrentControlCircuitPlot } from '@/utils/plotlyUtils'

interface Props {
  data: CurrentControlCircuitData
  record: ExperimentRecord
}

const props = defineProps<Props>()

const activeTab = ref('k1')

// 计算统计信息
const stats = computed(() => {
  return calculateCurrentControlCircuitStats(props.data)
})

// 前端绘图数据
const plotData = ref<any>({})

// 生成图表数据
const generatePlotData = () => {
  try {
    const chartConfig = generateCurrentControlCircuitPlot(
      {
        k1Data: props.data.k1Data,
        k01Data: props.data.k01Data
      },
      {
        width: 800,
        height: 600,
        smooth: true,
        lang: 'zh'
      }
    )
    plotData.value = chartConfig
  } catch (error) {
    console.error('生成图表数据失败:', error)
    plotData.value = {}
  }
}

// Plotly图表事件处理
const onPlotReady = (plotElement: any) => {
  console.log('图表已准备就绪:', plotElement)
}

const onPlotError = (error: Error) => {
  console.error('图表错误:', error)
}

// 格式化日期时间
const formatDateTime = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

// 组件挂载时生成图表数据
onMounted(() => {
  generatePlotData()
})

// 获取状态类型
const getStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    submitted: 'info',
    reviewed: 'warning', 
    approved: 'success',
    rejected: 'danger'
  }
  return typeMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    submitted: '已提交',
    reviewed: '已审核',
    approved: '已通过',
    rejected: '已拒绝'
  }
  return textMap[status] || status
}

// 处理图片加载错误
const handleImageError = () => {
  console.warn('实验图形加载失败')
}
</script>

<style scoped>
.current-control-circuit-detail {
  padding: 0;
}

.detail-section {
  margin-bottom: 20px;
}

.detail-section:last-child {
  margin-bottom: 0;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

.environment-info {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.environment-info h4 {
  margin: 0 0 15px 0;
  color: #606266;
  font-size: 14px;
}

.data-table-container {
  width: 100%;
}

.stats-info {
  margin-top: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.stats-info h5 {
  margin: 0 0 15px 0;
  color: #606266;
  font-size: 14px;
}

.missing-data {
  color: #f56c6c;
}

.plot-container {
  text-align: center;
  padding: 20px;
}

.plot-image {
  max-width: 100%;
  max-height: 500px;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.analysis-content {
  padding: 10px 0;
}

.analysis-text {
  margin-top: 15px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
  line-height: 1.6;
  color: #606266;
}

.notes-content {
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
  line-height: 1.6;
  color: #606266;
}

:deep(.el-descriptions__label) {
  font-weight: 600;
}

:deep(.el-card__header) {
  padding: 15px 20px;
  background-color: #fafafa;
}

:deep(.el-tabs__content) {
  padding: 20px 0 0 0;
}
</style>
