<template>
  <div class="generic-experiment-record-table">
    <!-- 页面标题 -->
    <div class="record-header">
      <h2>
        <el-icon><DataAnalysis /></el-icon>
        {{ experimentTypeName }}实验记录表
      </h2>
      <p>通用格式实验数据展示</p>
    </div>

    <!-- 基本信息 -->
    <el-card class="basic-info-card" shadow="never">
      <template #header>
        <div class="section-header">
          <el-icon><User /></el-icon>
          <span>基本信息</span>
        </div>
      </template>
      
      <el-descriptions :column="3" border size="small">
        <el-descriptions-item label="学生姓名">
          {{ studentName || '未知' }}
        </el-descriptions-item>
        <el-descriptions-item label="学号">
          {{ studentId || '未知' }}
        </el-descriptions-item>
        <el-descriptions-item label="提交时间">
          {{ formatDateTime(record.submitted_at) }}
        </el-descriptions-item>
        <el-descriptions-item label="实验状态">
          <el-tag :type="record.is_passed ? 'success' : 'danger'" v-if="record.is_passed !== null">
            {{ record.is_passed ? '通过' : '未通过' }}
          </el-tag>
          <span v-else>待评估</span>
        </el-descriptions-item>
        <el-descriptions-item label="得分">
          {{ record.score !== null ? record.score : '未评分' }}
        </el-descriptions-item>
        <el-descriptions-item label="审核状态">
          <el-tag :type="getStatusType(record.status)" size="small">
            {{ getStatusText(record.status) }}
          </el-tag>
        </el-descriptions-item>
      </el-descriptions>
    </el-card>

    <!-- 解析错误提示 -->
    <el-card v-if="parsedData && !parsedData.isValid" class="error-card" shadow="never">
      <template #header>
        <div class="section-header">
          <el-icon><WarningFilled /></el-icon>
          <span>数据解析错误</span>
        </div>
      </template>
      
      <el-alert
        v-for="(error, index) in parsedData.errors"
        :key="index"
        :title="error"
        type="error"
        :closable="false"
        style="margin-bottom: 10px;"
      />
    </el-card>

    <!-- 通用实验记录表 -->
    <el-card class="json-record-card" shadow="never">
      <template #header>
        <div class="section-header">
          <el-icon><Document /></el-icon>
          <span>实验记录表 (通用格式)</span>
          <div class="header-actions">
            <el-button size="small" @click="copyJson">复制JSON</el-button>
            <el-button size="small" @click="downloadJson">下载JSON</el-button>
          </div>
        </div>
      </template>

      <el-collapse v-model="activeCollapse" accordion>
        <!-- 原始提交数据 -->
        <el-collapse-item name="submission-data">
          <template #title>
            <div class="collapse-title">
              <el-icon><DataBoard /></el-icon>
              <span>原始提交数据</span>
            </div>
          </template>
          
          <div class="data-section">
            <el-input
              v-model="submissionDataJson"
              type="textarea"
              :rows="12"
              readonly
              class="json-textarea"
            />
          </div>
        </el-collapse-item>

        <!-- 实验图形部分 -->
        <el-collapse-item name="experiment-plot" v-if="plotData">
          <template #title>
            <div class="collapse-title">
              <el-icon><PictureRounded /></el-icon>
              <span>实验图形</span>
            </div>
          </template>

          <div class="plot-section">
            <div class="plot-container">
              <img :src="plotImageUrl" alt="实验图形" class="plot-image" @click="imageDialogVisible = true" />
              <div class="plot-actions">
                <el-button size="small" @click="downloadPlot">下载图片</el-button>
                <el-button size="small" @click="imageDialogVisible = true">查看大图</el-button>
              </div>
            </div>
            <div class="plot-info" v-if="plotData.chart_type">
              <p><strong>图表类型:</strong> {{ plotData.chart_type }}</p>
              <p v-if="plotData.title"><strong>标题:</strong> {{ plotData.title }}</p>
            </div>
          </div>
        </el-collapse-item>

        <!-- 实验分析部分 -->
        <el-collapse-item name="experiment-analysis" v-if="analysisData">
          <template #title>
            <div class="collapse-title">
              <el-icon><ChatDotRound /></el-icon>
              <span>实验分析：AI分析结果</span>
            </div>
          </template>

          <div class="analysis-section">
            <div class="analysis-content">
              <pre>{{ analysisData.message }}</pre>
              <div class="analysis-meta" v-if="analysisData.analyzed_at">
                <p><strong>分析时间:</strong> {{ formatDateTime(analysisData.analyzed_at) }}</p>
                <p><strong>分析结果:</strong>
                  <el-tag :type="analysisData.is_passed ? 'success' : 'danger'" v-if="analysisData.is_passed !== null">
                    {{ analysisData.is_passed ? '通过' : '未通过' }}
                  </el-tag>
                  <span v-else>待评估</span>
                </p>
              </div>
            </div>
          </div>
        </el-collapse-item>

        <!-- 完整JSON记录 -->
        <el-collapse-item name="complete-json">
          <template #title>
            <div class="collapse-title">
              <el-icon><Tickets /></el-icon>
              <span>完整JSON记录</span>
            </div>
          </template>
          
          <div class="json-section">
            <el-input
              v-model="displayJsonData"
              type="textarea"
              :rows="15"
              readonly
              class="json-textarea"
            />
          </div>
        </el-collapse-item>
      </el-collapse>
    </el-card>

    <!-- 图片预览对话框 -->
    <el-dialog v-model="imageDialogVisible" title="实验图形" width="80%" center>
      <div class="dialog-image-container">
        <img :src="plotImageUrl" alt="实验图形" class="dialog-image" />
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  User, 
  DataAnalysis, 
  PictureRounded, 
  ChatDotRound,
  Document,
  DataBoard,
  Tickets,
  WarningFilled
} from '@element-plus/icons-vue'
import type { 
  ExperimentRecord, 
  ParsedExperimentData 
} from '@/services/experimentDataParser'

interface Props {
  record: ExperimentRecord
  parsedData: ParsedExperimentData | null
  experimentType: string
}

const props = defineProps<Props>()

const activeCollapse = ref(['submission-data'])
const imageDialogVisible = ref(false)

// 实验类型名称
const experimentTypeName = computed(() => {
  const typeMap: Record<string, string> = {
    'current_control_circuit': '制流电路',
    'oscilloscope': '示波器',
    'unknown': '未知类型'
  }
  return typeMap[props.experimentType] || props.experimentType
})

// 提取学生信息
const studentName = computed(() => {
  try {
    const data = typeof props.record.submission_data === 'string' 
      ? JSON.parse(props.record.submission_data)
      : props.record.submission_data
    return data.student_name || data.name
  } catch {
    return null
  }
})

const studentId = computed(() => {
  try {
    const data = typeof props.record.submission_data === 'string' 
      ? JSON.parse(props.record.submission_data)
      : props.record.submission_data
    return data.student_id || data.id
  } catch {
    return null
  }
})

// 原始提交数据JSON
const submissionDataJson = computed(() => {
  try {
    const data = typeof props.record.submission_data === 'string' 
      ? JSON.parse(props.record.submission_data)
      : props.record.submission_data
    return JSON.stringify(data, null, 2)
  } catch {
    return props.record.submission_data as string
  }
})

// 从submission_data中提取图形数据
const plotData = computed(() => {
  try {
    const data = typeof props.record.submission_data === 'string'
      ? JSON.parse(props.record.submission_data)
      : props.record.submission_data
    return data.plot_data || null
  } catch {
    return null
  }
})

// 从submission_data中提取分析数据
const analysisData = computed(() => {
  try {
    const data = typeof props.record.submission_data === 'string'
      ? JSON.parse(props.record.submission_data)
      : props.record.submission_data
    return data.analysis_result || null
  } catch {
    return null
  }
})

// 显示用的中文格式JSON数据（仅用于前端显示）
const displayJsonData = computed(() => {
  const displayData = {
    "实验数据": props.parsedData?.data || props.record.submission_data,
    "实验图形": plotData.value ? {
      "图片格式": "base64编码的PNG图片",
      "图表类型": plotData.value.chart_type,
      "图表标题": plotData.value.title
    } : null,
    "实验分析": analysisData.value ? {
      "分析结果": analysisData.value.message,
      "是否通过": analysisData.value.is_passed,
      "分析时间": analysisData.value.analyzed_at
    } : null,
    "实验信息": {
      "实验类型": props.experimentType,
      "是否通过": props.record.is_passed,
      "得分": props.record.score,
      "提交时间": props.record.submitted_at,
      "审核状态": props.record.status
    }
  }
  return JSON.stringify(displayData, null, 2)
})

// 图片URL
const plotImageUrl = computed(() => {
  if (!plotData.value?.image_base64) return ''

  if (plotData.value.image_base64.startsWith('data:')) {
    return plotData.value.image_base64
  }

  return `data:image/png;base64,${plotData.value.image_base64}`
})

// 格式化日期时间
const formatDateTime = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

// 获取状态类型
const getStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    submitted: 'info',
    reviewed: 'warning', 
    approved: 'success',
    rejected: 'danger'
  }
  return typeMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    submitted: '已提交',
    reviewed: '已审核',
    approved: '已通过',
    rejected: '已拒绝'
  }
  return textMap[status] || status
}

// 复制JSON（复制显示格式的中文版本）
const copyJson = async () => {
  try {
    await navigator.clipboard.writeText(displayJsonData.value)
    ElMessage.success('JSON数据已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

// 下载JSON（下载显示格式的中文版本）
const downloadJson = () => {
  const blob = new Blob([displayJsonData.value], { type: 'application/json; charset=utf-8' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `${experimentTypeName.value}实验_${studentId.value || 'unknown'}_${new Date().toISOString().split('T')[0]}.json`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
  ElMessage.success('JSON文件下载成功')
}

// 下载图片
const downloadPlot = () => {
  if (!plotImageUrl.value) return
  
  const a = document.createElement('a')
  a.href = plotImageUrl.value
  a.download = `${experimentTypeName.value}实验图形_${studentId.value || 'unknown'}_${new Date().toISOString().split('T')[0]}.png`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  ElMessage.success('图片下载成功')
}
</script>

<style scoped>
.generic-experiment-record-table {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.record-header {
  text-align: center;
  margin-bottom: 30px;
}

.record-header h2 {
  color: #2c3e50;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.record-header p {
  color: #7f8c8d;
  margin: 0;
}

.basic-info-card,
.error-card,
.json-record-card {
  margin-bottom: 20px;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #2c3e50;
}

.header-actions {
  margin-left: auto;
  display: flex;
  gap: 8px;
}

.collapse-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.data-section,
.plot-section,
.analysis-section,
.json-section {
  padding: 20px 0;
}

.json-textarea {
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.plot-container {
  text-align: center;
}

.plot-image {
  max-width: 100%;
  max-height: 500px;
  border: 1px solid #ddd;
  border-radius: 8px;
  cursor: pointer;
  transition: transform 0.2s;
}

.plot-image:hover {
  transform: scale(1.02);
}

.plot-actions {
  margin-top: 15px;
  display: flex;
  justify-content: center;
  gap: 10px;
}

.analysis-content {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
}

.analysis-content pre {
  white-space: pre-wrap;
  word-wrap: break-word;
  margin: 0;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: #2c3e50;
}

.dialog-image-container {
  text-align: center;
}

.dialog-image {
  max-width: 100%;
  max-height: 70vh;
  border-radius: 8px;
}
</style>
