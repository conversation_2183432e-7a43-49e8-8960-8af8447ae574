<template>
  <div class="oscilloscope-record-table">
    <!-- 页面标题 -->
    <div class="record-header">
      <h2>
        <el-icon><DataAnalysis /></el-icon>
        示波器实验记录表
      </h2>
      <p>实验提交数据按JSON格式展示</p>
    </div>

    <!-- 基本信息 -->
    <el-card class="basic-info-card" shadow="never">
      <template #header>
        <div class="section-header">
          <el-icon><User /></el-icon>
          <span>基本信息</span>
        </div>
      </template>
      
      <el-descriptions :column="3" border size="small">
        <el-descriptions-item label="学生姓名">
          {{ data.student_name || '未知' }}
        </el-descriptions-item>
        <el-descriptions-item label="学号">
          {{ data.student_id || '未知' }}
        </el-descriptions-item>
        <el-descriptions-item label="提交时间">
          {{ formatDateTime(record.submitted_at) }}
        </el-descriptions-item>
        <el-descriptions-item label="实验状态">
          <el-tag :type="record.is_passed ? 'success' : 'danger'" v-if="record.is_passed !== null">
            {{ record.is_passed ? '通过' : '未通过' }}
          </el-tag>
          <span v-else>待评估</span>
        </el-descriptions-item>
        <el-descriptions-item label="得分">
          {{ record.score !== null ? record.score : '未评分' }}
        </el-descriptions-item>
        <el-descriptions-item label="审核状态">
          <el-tag :type="getStatusType(record.status)" size="small">
            {{ getStatusText(record.status) }}
          </el-tag>
        </el-descriptions-item>
      </el-descriptions>
    </el-card>

    <!-- JSON格式的实验记录表 -->
    <el-card class="json-record-card" shadow="never">
      <template #header>
        <div class="section-header">
          <el-icon><Document /></el-icon>
          <span>实验记录表 (JSON格式)</span>
          <div class="header-actions">
            <el-button size="small" @click="copyJson">复制JSON</el-button>
            <el-button size="small" @click="downloadJson">下载JSON</el-button>
          </div>
        </div>
      </template>

      <el-collapse v-model="activeCollapse" accordion>
        <!-- 实验数据部分 -->
        <el-collapse-item name="experiment-data">
          <template #title>
            <div class="collapse-title">
              <el-icon><DataBoard /></el-icon>
              <span>实验数据：示波器测量数据</span>
            </div>
          </template>
          
          <div class="data-section">
            <!-- 基本参数 -->
            <div class="data-group">
              <h4>基本参数</h4>
              <el-descriptions :column="2" border size="small">
                <el-descriptions-item label="频率 (Hz)">
                  {{ data.frequency }}
                </el-descriptions-item>
                <el-descriptions-item label="幅度 (V)">
                  {{ data.amplitude }}
                </el-descriptions-item>
                <el-descriptions-item label="波形类型">
                  {{ data.waveform_type }}
                </el-descriptions-item>
                <el-descriptions-item label="备注">
                  {{ data.notes || '无' }}
                </el-descriptions-item>
              </el-descriptions>
            </div>

            <!-- 测量数据表格 -->
            <div class="data-group">
              <h4>时间-电压测量数据</h4>
              <el-table :data="data.measurements" border stripe size="small" class="data-table" max-height="400">
                <el-table-column prop="time" label="时间 (s)" width="150" align="center">
                  <template #default="{ row }">
                    {{ row.time?.toFixed(6) || '-' }}
                  </template>
                </el-table-column>
                <el-table-column prop="voltage" label="电压 (V)" width="150" align="center">
                  <template #default="{ row }">
                    {{ row.voltage?.toFixed(4) || '-' }}
                  </template>
                </el-table-column>
              </el-table>
            </div>

            <!-- 原始JSON数据 -->
            <div class="data-group">
              <h4>原始实验数据 (JSON)</h4>
              <el-input
                v-model="experimentDataJson"
                type="textarea"
                :rows="8"
                readonly
                class="json-textarea"
              />
            </div>
          </div>
        </el-collapse-item>

        <!-- 实验图形部分 -->
        <el-collapse-item name="experiment-plot" v-if="record.plot_data">
          <template #title>
            <div class="collapse-title">
              <el-icon><PictureRounded /></el-icon>
              <span>实验图形：波形图</span>
            </div>
          </template>
          
          <div class="plot-section">
            <div class="plot-container">
              <img :src="plotImageUrl" alt="示波器波形图" class="plot-image" @click="imageDialogVisible = true" />
              <div class="plot-actions">
                <el-button size="small" @click="downloadPlot">下载图片</el-button>
                <el-button size="small" @click="imageDialogVisible = true">查看大图</el-button>
              </div>
            </div>
          </div>
        </el-collapse-item>

        <!-- 实验分析部分 -->
        <el-collapse-item name="experiment-analysis" v-if="record.analysis_result">
          <template #title>
            <div class="collapse-title">
              <el-icon><ChatDotRound /></el-icon>
              <span>实验分析：AI分析结果</span>
            </div>
          </template>
          
          <div class="analysis-section">
            <div class="analysis-content">
              <pre>{{ record.analysis_result }}</pre>
            </div>
          </div>
        </el-collapse-item>

        <!-- 完整JSON记录 -->
        <el-collapse-item name="complete-json">
          <template #title>
            <div class="collapse-title">
              <el-icon><Tickets /></el-icon>
              <span>完整JSON记录</span>
            </div>
          </template>
          
          <div class="json-section">
            <el-input
              v-model="displayJsonData"
              type="textarea"
              :rows="15"
              readonly
              class="json-textarea"
            />
          </div>
        </el-collapse-item>
      </el-collapse>
    </el-card>

    <!-- 图片预览对话框 -->
    <el-dialog v-model="imageDialogVisible" title="实验图形" width="80%" center>
      <div class="dialog-image-container">
        <img :src="plotImageUrl" alt="示波器波形图" class="dialog-image" />
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  User, 
  DataAnalysis, 
  PictureRounded, 
  ChatDotRound,
  Document,
  DataBoard,
  Tickets
} from '@element-plus/icons-vue'
import type { 
  OscilloscopeData, 
  ExperimentRecord 
} from '@/services/experimentDataParser'

interface Props {
  data: OscilloscopeData
  record: ExperimentRecord
}

const props = defineProps<Props>()

const activeCollapse = ref(['experiment-data'])
const imageDialogVisible = ref(false)

// 实验数据JSON
const experimentDataJson = computed(() => {
  const experimentData = {
    frequency: props.data.frequency,
    amplitude: props.data.amplitude,
    waveform_type: props.data.waveform_type,
    measurements: props.data.measurements,
    notes: props.data.notes
  }
  return JSON.stringify(experimentData, null, 2)
})

// 完整JSON数据（使用英文key避免编码问题）
const completeJsonData = computed(() => {
  const completeData = {
    "experiment_data": {
      "frequency": props.data.frequency,
      "amplitude": props.data.amplitude,
      "waveform_type": props.data.waveform_type,
      "measurements": props.data.measurements.map(item => ({
        "time": item.time,
        "voltage": item.voltage
      })),
      "notes": props.data.notes
    },
    "experiment_chart": props.record.plot_data ? "base64图片数据" : null,
    "experiment_analysis": props.record.analysis_result
  }
  return JSON.stringify(completeData, null, 2)
})

// 显示用的中文格式JSON数据（仅用于前端显示）
const displayJsonData = computed(() => {
  const displayData = {
    "实验数据": {
      "频率(Hz)": props.data.frequency,
      "幅度(V)": props.data.amplitude,
      "波形类型": props.data.waveform_type,
      "测量数据": props.data.measurements.map(item => ({
        "时间(s)": item.time,
        "电压(V)": item.voltage
      })),
      "备注": props.data.notes
    },
    "实验图形": props.record.plot_data ? "base64图片数据" : null,
    "实验分析": props.record.analysis_result
  }
  return JSON.stringify(displayData, null, 2)
})

// 图片URL
const plotImageUrl = computed(() => {
  if (!props.record.plot_data) return ''
  
  if (props.record.plot_data.startsWith('data:')) {
    return props.record.plot_data
  }
  
  return `data:image/png;base64,${props.record.plot_data}`
})

// 格式化日期时间
const formatDateTime = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

// 获取状态类型
const getStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    submitted: 'info',
    reviewed: 'warning', 
    approved: 'success',
    rejected: 'danger'
  }
  return typeMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    submitted: '已提交',
    reviewed: '已审核',
    approved: '已通过',
    rejected: '已拒绝'
  }
  return textMap[status] || status
}

// 复制JSON（复制显示格式的中文版本）
const copyJson = async () => {
  try {
    await navigator.clipboard.writeText(displayJsonData.value)
    ElMessage.success('JSON数据已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

// 下载JSON（下载显示格式的中文版本）
const downloadJson = () => {
  const blob = new Blob([displayJsonData.value], { type: 'application/json; charset=utf-8' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `示波器实验_${props.data.student_id}_${new Date().toISOString().split('T')[0]}.json`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
  ElMessage.success('JSON文件下载成功')
}

// 下载图片
const downloadPlot = () => {
  if (!plotImageUrl.value) return
  
  const a = document.createElement('a')
  a.href = plotImageUrl.value
  a.download = `示波器实验图形_${props.data.student_id}_${new Date().toISOString().split('T')[0]}.png`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  ElMessage.success('图片下载成功')
}
</script>

<style scoped>
.oscilloscope-record-table {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.record-header {
  text-align: center;
  margin-bottom: 30px;
}

.record-header h2 {
  color: #2c3e50;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.record-header p {
  color: #7f8c8d;
  margin: 0;
}

.basic-info-card,
.json-record-card {
  margin-bottom: 20px;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #2c3e50;
}

.header-actions {
  margin-left: auto;
  display: flex;
  gap: 8px;
}

.collapse-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.data-section {
  padding: 20px 0;
}

.data-group {
  margin-bottom: 30px;
}

.data-group h4 {
  color: #2c3e50;
  margin-bottom: 15px;
  padding-bottom: 8px;
  border-bottom: 2px solid #e1e8ed;
}

.data-table {
  margin-top: 10px;
}

.json-textarea {
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.plot-section {
  padding: 20px 0;
}

.plot-container {
  text-align: center;
}

.plot-image {
  max-width: 100%;
  max-height: 500px;
  border: 1px solid #ddd;
  border-radius: 8px;
  cursor: pointer;
  transition: transform 0.2s;
}

.plot-image:hover {
  transform: scale(1.02);
}

.plot-actions {
  margin-top: 15px;
  display: flex;
  justify-content: center;
  gap: 10px;
}

.analysis-section {
  padding: 20px 0;
}

.analysis-content {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
}

.analysis-content pre {
  white-space: pre-wrap;
  word-wrap: break-word;
  margin: 0;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: #2c3e50;
}

.json-section {
  padding: 20px 0;
}

.dialog-image-container {
  text-align: center;
}

.dialog-image {
  max-width: 100%;
  max-height: 70vh;
  border-radius: 8px;
}
</style>
