<template>
  <div class="data-input-table">
    <div class="table-header">
      <h3>{{ title }}</h3>
      <div class="table-actions">
        <el-button
          v-if="showValidate"
          type="success"
          :icon="Check"
          @click="validateData"
          :loading="validating"
        >
          验证数据
        </el-button>
        <el-button
          v-if="showClear"
          type="warning"
          :icon="Delete"
          @click="clearData"
        >
          清空数据
        </el-button>
      </div>
    </div>
    
    <el-table
      :data="tableData"
      border
      stripe
      class="data-table"
      :class="{ 'validation-error': hasValidationError }"
    >
      <el-table-column
        v-for="column in columns"
        :key="column.prop"
        :prop="column.prop"
        :label="column.label"
        :width="column.width"
        :min-width="column.minWidth"
        :align="column.align || 'center'"
      >
        <template #default="{ row, $index }">
          <div v-if="column.type === 'input'">
            <el-input-number
              v-model="row[column.prop]"
              :precision="column.precision || 2"
              :step="column.step || 0.01"
              :min="column.min"
              :max="column.max"
              :placeholder="column.placeholder"
              :disabled="column.disabled"
              size="small"
              controls-position="right"
              @change="onDataChange($index, column.prop, row[column.prop])"
            />
          </div>
          <div v-else-if="column.type === 'text'">
            <el-input
              v-model="row[column.prop]"
              :placeholder="column.placeholder"
              :disabled="column.disabled"
              size="small"
              @change="onDataChange($index, column.prop, row[column.prop])"
            />
          </div>
          <span v-else>
            {{ formatValue(row[column.prop], column) }}
          </span>
        </template>
      </el-table-column>
    </el-table>
    
    <div v-if="validationMessage" class="validation-message">
      <el-alert
        :title="validationMessage"
        :type="validationSuccess ? 'success' : 'error'"
        :closable="false"
        show-icon
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { Check, Delete } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

interface TableColumn {
  prop: string
  label: string
  type?: 'input' | 'text' | 'display'
  width?: string | number
  minWidth?: string | number
  align?: 'left' | 'center' | 'right'
  precision?: number
  step?: number
  min?: number
  max?: number
  placeholder?: string
  disabled?: boolean
  formatter?: (value: any) => string
}

interface Props {
  title: string
  columns: TableColumn[]
  data: Record<string, any>[]
  showValidate?: boolean
  showClear?: boolean
  validationRules?: (data: Record<string, any>[]) => { valid: boolean; message: string }
}

const props = withDefaults(defineProps<Props>(), {
  showValidate: true,
  showClear: true
})

const emit = defineEmits<{
  'data-change': [data: Record<string, any>[]]
  'validation-result': [result: { valid: boolean; message: string }]
}>()

// 响应式数据
const tableData = ref([...props.data])
const validating = ref(false)
const validationMessage = ref('')
const validationSuccess = ref(false)

// 计算属性
const hasValidationError = computed(() => validationMessage.value && !validationSuccess.value)

// 监听props.data变化
watch(() => props.data, (newData) => {
  tableData.value = [...newData]
}, { deep: true })

// 数据变化处理
const onDataChange = (index: number, prop: string, value: any) => {
  tableData.value[index][prop] = value
  emit('data-change', [...tableData.value])
  
  // 清除之前的验证消息
  if (validationMessage.value) {
    validationMessage.value = ''
  }
}

// 验证数据
const validateData = async () => {
  if (!props.validationRules) {
    ElMessage.warning('未设置验证规则')
    return
  }
  
  validating.value = true
  
  try {
    // 模拟异步验证
    await new Promise(resolve => setTimeout(resolve, 500))
    
    const result = props.validationRules(tableData.value)
    validationMessage.value = result.message
    validationSuccess.value = result.valid
    
    emit('validation-result', result)
    
    if (result.valid) {
      ElMessage.success('数据验证通过')
    } else {
      ElMessage.error('数据验证失败')
    }
  } catch (error) {
    ElMessage.error('验证过程中出现错误')
  } finally {
    validating.value = false
  }
}

// 清空数据
const clearData = () => {
  tableData.value.forEach(row => {
    props.columns.forEach(column => {
      if (column.type === 'input' || column.type === 'text') {
        row[column.prop] = column.type === 'input' ? 0 : ''
      }
    })
  })
  
  emit('data-change', [...tableData.value])
  validationMessage.value = ''
  ElMessage.success('数据已清空')
}

// 格式化值显示
const formatValue = (value: any, column: TableColumn) => {
  if (column.formatter) {
    return column.formatter(value)
  }
  
  if (typeof value === 'number') {
    return value.toFixed(column.precision || 2)
  }
  
  return value || '-'
}
</script>

<style scoped>
.data-input-table {
  margin: 20px 0;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.table-header h3 {
  margin: 0;
  color: #303133;
}

.table-actions {
  display: flex;
  gap: 10px;
}

.data-table {
  width: 100%;
}

.data-table.validation-error {
  border-color: #f56c6c;
}

.validation-message {
  margin-top: 15px;
}

:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-input-number .el-input__inner) {
  text-align: center;
}
</style>
