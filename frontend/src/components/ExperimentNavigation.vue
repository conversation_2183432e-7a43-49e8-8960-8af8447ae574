<template>
  <div class="experiment-navigation">
    <div class="nav-buttons">
      <el-button
        v-if="showPrev"
        :disabled="disabled"
        @click="$emit('prev')"
        :icon="ArrowLeft"
      >
        {{ prevText }}
      </el-button>
      
      <el-button
        v-if="showNext"
        type="primary"
        :disabled="disabled || (hasValidation && !validated)"
        @click="handleNext"
        :icon="ArrowRight"
        :loading="loading"
      >
        {{ nextText }}
      </el-button>
    </div>
    
    <div v-if="showProgress" class="progress-info">
      <span class="step-info">
        步骤 {{ currentStep }} / {{ totalSteps }}
        <span v-if="currentSubstep && totalSubsteps">
          - {{ currentSubstep }} / {{ totalSubsteps }}
        </span>
      </span>
      
      <el-progress
        :percentage="progressPercentage"
        :stroke-width="6"
        :show-text="false"
        class="progress-bar"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ArrowLeft, ArrowRight } from '@element-plus/icons-vue'

interface Props {
  currentStep: number
  totalSteps: number
  currentSubstep?: number
  totalSubsteps?: number
  showPrev?: boolean
  showNext?: boolean
  showProgress?: boolean
  prevText?: string
  nextText?: string
  disabled?: boolean
  loading?: boolean
  hasValidation?: boolean
  validated?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showPrev: true,
  showNext: true,
  showProgress: true,
  prevText: '上一步',
  nextText: '下一步',
  disabled: false,
  loading: false,
  hasValidation: false,
  validated: true
})

const emit = defineEmits<{
  prev: []
  next: []
  validate: []
}>()

// 计算进度百分比
const progressPercentage = computed(() => {
  if (props.currentSubstep && props.totalSubsteps) {
    // 如果有子步骤，计算更精确的进度
    const stepProgress = (props.currentStep - 1) / props.totalSteps
    const substepProgress = (props.currentSubstep - 1) / props.totalSubsteps / props.totalSteps
    return Math.round((stepProgress + substepProgress) * 100)
  } else {
    // 只有主步骤的进度
    return Math.round((props.currentStep / props.totalSteps) * 100)
  }
})

// 处理下一步点击
const handleNext = () => {
  if (props.hasValidation && !props.validated) {
    emit('validate')
  } else {
    emit('next')
  }
}
</script>

<style scoped>
.experiment-navigation {
  margin-top: 20px;
  padding: 20px;
  border-top: 1px solid #e4e7ed;
  background-color: #fafafa;
}

.nav-buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.nav-buttons .el-button {
  min-width: 100px;
}

.progress-info {
  text-align: center;
}

.step-info {
  display: block;
  margin-bottom: 10px;
  font-size: 14px;
  color: #606266;
}

.progress-bar {
  max-width: 400px;
  margin: 0 auto;
}
</style>
