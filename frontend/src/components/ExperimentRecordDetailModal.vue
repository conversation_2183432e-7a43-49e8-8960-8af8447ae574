<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="1200px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="experiment-record-modal"
  >
    <div class="modal-content">
      <!-- 实验详情内容 -->
      <div v-if="record" class="experiment-detail">
        <!-- 使用工厂模式组件 -->
        <ExperimentRecordTableFactory
          :record="record"
          :experiment-type="record.experiment_code"
        />
      </div>

      <!-- 无数据状态 -->
      <div v-else class="no-data">
        <el-empty description="无实验数据" />
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button
          v-if="record"
          type="primary"
          @click="handleExport"
          :loading="exporting"
        >
          导出数据
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import ExperimentRecordTableFactory from './experiment-details/ExperimentRecordTableFactory.vue'
import {
  type ExperimentRecord
} from '@/services/experimentDataParser'

interface Props {
  visible: boolean
  record: ExperimentRecord | null
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'close'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const dialogVisible = ref(false)
const exporting = ref(false)

// 对话框标题
const dialogTitle = computed(() => {
  if (!props.record) return '实验记录详情'
  return `${props.record.experiment_name} - 详情`
})



// 监听visible变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
})

// 监听dialogVisible变化
watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
})

// 处理关闭
const handleClose = () => {
  dialogVisible.value = false
  emit('close')
}

// 处理导出
const handleExport = async () => {
  if (!props.record) return

  exporting.value = true

  try {
    // 准备导出数据
    const exportData = {
      experiment_info: {
        id: props.record.id,
        experiment_name: props.record.experiment_name,
        experiment_code: props.record.experiment_code,
        submitted_at: props.record.submitted_at,
        is_passed: props.record.is_passed,
        score: props.record.score,
        status: props.record.status
      },
      submission_data: props.record.submission_data,
      analysis_result: props.record.analysis_result,
      has_plot: !!props.record.plot_data
    }

    // 创建下载链接
    const dataStr = JSON.stringify(exportData, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(dataBlob)

    // 创建下载链接并触发下载
    const link = document.createElement('a')
    link.href = url
    link.download = `实验记录_${props.record.id}_${new Date().toISOString().slice(0, 10)}.json`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    // 清理URL对象
    URL.revokeObjectURL(url)

    ElMessage.success('数据导出成功')

  } catch (err) {
    console.error('导出数据失败:', err)
    ElMessage.error('导出数据失败')
  } finally {
    exporting.value = false
  }
}
</script>

<style scoped>
.experiment-record-modal {
  --el-dialog-padding-primary: 20px;
}

.modal-content {
  min-height: 200px;
}

.error-alert {
  margin-bottom: 20px;
}

.error-list {
  margin: 10px 0 0 20px;
  padding: 0;
}

.error-list li {
  margin-bottom: 5px;
  color: #e6a23c;
}

.experiment-detail {
  width: 100%;
}

.oscilloscope-detail,
.unknown-experiment {
  width: 100%;
}

.notes-section,
.raw-data-section {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.notes-section h4,
.raw-data-section h4 {
  margin: 0 0 15px 0;
  color: #606266;
  font-size: 14px;
  font-weight: 600;
}

.notes-section p {
  margin: 0;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
  line-height: 1.6;
  color: #606266;
}

.no-data {
  text-align: center;
  padding: 40px 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

:deep(.el-dialog__body) {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;
}

:deep(.el-dialog__header) {
  padding: 20px 20px 10px 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px 20px 20px;
}
</style>
