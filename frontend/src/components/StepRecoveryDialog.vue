<template>
  <el-dialog
    v-model="visible"
    title="步骤恢复助手"
    width="600px"
    :close-on-click-modal="false"
  >
    <div class="recovery-dialog">
      <el-alert
        title="检测到步骤可能卡住"
        type="warning"
        :closable="false"
        show-icon
      >
        <template #default>
          <p>当前步骤: {{ currentStepTitle }}</p>
          <p>问题: {{ problemDescription }}</p>
        </template>
      </el-alert>

      <div class="recovery-options">
        <h4>恢复选项:</h4>
        
        <el-card class="option-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <el-icon><Refresh /></el-icon>
              <span>重置当前步骤</span>
            </div>
          </template>
          <p>清除当前步骤的所有数据，重新开始</p>
          <el-button type="primary" @click="resetCurrentStep">
            重置步骤
          </el-button>
        </el-card>

        <el-card class="option-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <el-icon><ArrowRight /></el-icon>
              <span>跳过验证</span>
            </div>
          </template>
          <p>暂时跳过当前步骤的验证，稍后可以回来完善</p>
          <el-button type="warning" @click="skipValidation">
            跳过验证
          </el-button>
        </el-card>

        <el-card class="option-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <el-icon><Back /></el-icon>
              <span>返回上一步</span>
            </div>
          </template>
          <p>返回上一步重新操作</p>
          <el-button @click="goToPreviousStep">
            返回上一步
          </el-button>
        </el-card>

        <el-card class="option-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <el-icon><Document /></el-icon>
              <span>查看帮助</span>
            </div>
          </template>
          <p>查看当前步骤的详细说明和常见问题</p>
          <el-button type="info" @click="showHelp">
            查看帮助
          </el-button>
        </el-card>
      </div>

      <div class="debug-info" v-if="showDebugInfo">
        <el-collapse>
          <el-collapse-item title="调试信息" name="debug">
            <pre>{{ debugInfo }}</pre>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="visible = false">取消</el-button>
        <el-button type="info" @click="toggleDebugInfo">
          {{ showDebugInfo ? '隐藏' : '显示' }}调试信息
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh, ArrowRight, Back, Document } from '@element-plus/icons-vue'

interface Props {
  modelValue: boolean
  currentStep: number
  currentSubstep?: number
  stepTitle: string
  problemDescription: string
  debugData?: any
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'reset-step': []
  'skip-validation': []
  'go-previous': []
  'show-help': []
}>()

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const showDebugInfo = ref(false)

const currentStepTitle = computed(() => {
  if (props.currentSubstep) {
    return `${props.stepTitle} - 子步骤 ${props.currentSubstep}`
  }
  return props.stepTitle
})

const debugInfo = computed(() => {
  return JSON.stringify({
    currentStep: props.currentStep,
    currentSubstep: props.currentSubstep,
    stepTitle: props.stepTitle,
    problemDescription: props.problemDescription,
    debugData: props.debugData
  }, null, 2)
})

const resetCurrentStep = () => {
  ElMessage.success('正在重置当前步骤...')
  emit('reset-step')
  visible.value = false
}

const skipValidation = () => {
  ElMessage.warning('已跳过当前步骤验证')
  emit('skip-validation')
  visible.value = false
}

const goToPreviousStep = () => {
  emit('go-previous')
  visible.value = false
}

const showHelp = () => {
  emit('show-help')
  visible.value = false
}

const toggleDebugInfo = () => {
  showDebugInfo.value = !showDebugInfo.value
}
</script>

<style scoped>
.recovery-dialog {
  padding: 10px 0;
}

.recovery-options {
  margin-top: 20px;
}

.recovery-options h4 {
  margin-bottom: 15px;
  color: #303133;
}

.option-card {
  margin-bottom: 15px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.option-card .el-card__body {
  padding-top: 10px;
}

.option-card p {
  margin: 10px 0;
  color: #606266;
  font-size: 14px;
}

.debug-info {
  margin-top: 20px;
}

.debug-info pre {
  background-color: #f5f7fa;
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
}
</style>
