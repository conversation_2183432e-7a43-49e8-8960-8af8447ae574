<template>
  <div class="step-indicator">
    <el-steps
      :active="currentStep - 1"
      :process-status="processStatus"
      :finish-status="finishStatus"
      :align-center="alignCenter"
      :direction="direction"
      :space="space"
    >
      <el-step
        v-for="(step, index) in steps"
        :key="index"
        :title="step.title"
        :description="step.description"
        :icon="step.icon"
        :status="getStepStatus(index + 1)"
      >
        <template v-if="step.customIcon" #icon>
          <component :is="step.customIcon" />
        </template>
      </el-step>
    </el-steps>

    <!-- 子步骤指示器 -->
    <div v-if="showSubsteps && currentSubsteps.length > 0" class="substep-indicator">
      <div class="substep-title">{{ currentStepTitle }} - 详细步骤</div>
      <el-steps
        :active="safeCurrentSubstep - 1"
        :process-status="processStatus"
        :finish-status="finishStatus"
        direction="horizontal"
        size="small"
        simple
      >
        <el-step
          v-for="(substep, index) in currentSubsteps"
          :key="index"
          :title="substep.title"
          :description="substep.description"
        />
      </el-steps>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Step {
  title: string
  description?: string
  icon?: string
  customIcon?: any
  substeps?: Substep[]
}

interface Substep {
  title: string
  description?: string
}

interface Props {
  steps: Step[]
  currentStep: number
  currentSubstep?: number
  processStatus?: 'wait' | 'process' | 'finish' | 'error' | 'success'
  finishStatus?: 'wait' | 'process' | 'finish' | 'error' | 'success'
  alignCenter?: boolean
  direction?: 'horizontal' | 'vertical'
  space?: string | number
  showSubsteps?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  processStatus: 'process',
  finishStatus: 'success',
  alignCenter: true,
  direction: 'horizontal',
  showSubsteps: true
})

// 计算当前步骤的子步骤
const currentSubsteps = computed(() => {
  const currentStepData = props.steps[props.currentStep - 1]
  return currentStepData?.substeps || []
})

// 计算当前步骤标题
const currentStepTitle = computed(() => {
  const currentStepData = props.steps[props.currentStep - 1]
  return currentStepData?.title || ''
})

// 子步骤安全值，避免未传递时报错
const safeCurrentSubstep = computed(() => (props.currentSubstep ?? 1))

// 获取步骤状态
const getStepStatus = (stepNumber: number) => {
  if (stepNumber < props.currentStep) {
    return props.finishStatus
  } else if (stepNumber === props.currentStep) {
    return props.processStatus
  } else {
    return 'wait'
  }
}
</script>

<style scoped>
.step-indicator {
  margin: 20px 0;
  padding: 20px;
  background-color: #fafafa;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.substep-indicator {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;
}

.substep-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 15px;
  text-align: center;
}

:deep(.el-steps) {
  margin: 0;
}

:deep(.el-step__title) {
  font-size: 14px;
  font-weight: 500;
}

:deep(.el-step__description) {
  font-size: 12px;
  color: #909399;
}

/* 垂直方向样式调整 */
.step-indicator[direction="vertical"] {
  padding: 30px 20px;
}

:deep(.el-steps--vertical .el-step__main) {
  padding-left: 15px;
}

/* 小尺寸步骤条样式 */
:deep(.el-steps--small .el-step__title) {
  font-size: 12px;
}

:deep(.el-steps--small .el-step__description) {
  font-size: 11px;
}
</style>
