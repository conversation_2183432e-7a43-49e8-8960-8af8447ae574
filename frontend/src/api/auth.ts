/**
 * 认证相关API
 */

import { request } from './index'
import type { LoginRequest, LoginResponse, User } from '@/types'

export const authApi = {
  // 用户登录
  login(data: LoginRequest): Promise<LoginResponse> {
    return request.post<LoginResponse>('/api/auth/login', data).then(res => res as LoginResponse)
  },
  
  // 获取当前用户信息
  getCurrentUser(): Promise<User> {
    return request.get<User>('/api/auth/me').then(res => res as User)
  },
  
  // 用户登出
  logout(): Promise<any> {
    return request.post('/api/auth/logout')
  }
}
