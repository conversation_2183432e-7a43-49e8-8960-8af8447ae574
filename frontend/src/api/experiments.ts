/**
 * 实验相关API
 */

import { request } from './index'
import type {
  ExperimentType,
  ExperimentSubmissionRequest,
  ExperimentSubmissionResponse,
  PlotGenerationRequest,
  PlotGenerationResponse,
  AnalysisRequest,
  AnalysisResponse,
  ExperimentRecord
} from '@/types'

export const experimentsApi = {
  // 获取所有实验类型
  getExperimentTypes(): Promise<ExperimentType[]> {
    return request.get<ExperimentType[]>('/api/experiments/types').then(res => res as ExperimentType[])
  },
  
  // 获取特定实验类型
  getExperimentType(experimentCode: string): Promise<ExperimentType> {
    return request.get<ExperimentType>(`/api/experiments/types/${experimentCode}`).then(res => res as ExperimentType)
  },
  
  // 提交实验数据
  submitExperiment(data: ExperimentSubmissionRequest): Promise<ExperimentSubmissionResponse> {
    return request.post<ExperimentSubmissionResponse>('/api/experiments/submit', data).then(res => res as ExperimentSubmissionResponse)
  },
  
  // 获取实验记录
  getExperimentRecords(experimentCode?: string): Promise<any> {
    const params = experimentCode ? { experiment_code: experimentCode } : {}
    return request.get('/api/experiments/records', { params })
  },
  
  // 生成图形
  generatePlot(data: PlotGenerationRequest): Promise<PlotGenerationResponse> {
    return request.post<PlotGenerationResponse>('/api/experiments/plot', data).then(res => res as PlotGenerationResponse)
  },
  
  // 分析实验数据
  analyzeExperiment(data: AnalysisRequest): Promise<AnalysisResponse> {
    return request.post<AnalysisResponse>('/api/experiments/analyze', data).then(res => res as AnalysisResponse)
  }
}
