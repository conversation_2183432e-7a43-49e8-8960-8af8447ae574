/**
 * 管理后台相关API
 */

import { request } from './index'

export interface AdminRecordListParams {
  page?: number
  page_size?: number
  experiment_code?: string
  student_no?: string
  student_name?: string
  date_from?: string
  date_to?: string
  status?: string
}

export const adminApi = {
  // 仪表盘概览统计
  getOverviewStats() {
    return request.get('/api/admin/statistics/overview')
  },

  // 实验统计（按实验类型聚合）
  getExperimentStatistics() {
    return request.get('/api/admin/statistics/experiments')
  },

  // 学生列表（可选按班级筛选）
  getStudents(params?: { class_id?: number }) {
    return request.get('/api/admin/students', { params })
  },

  // 班级列表
  getClasses() {
    return request.get('/api/admin/classes')
  },

  // 获取实验记录（分页）
  getExperimentRecords(params: AdminRecordListParams) {
    return request.get('/api/admin/records', { params })
  },

  // 获取单条记录详情
  getExperimentRecordDetail(recordId: number) {
    return request.get(`/api/admin/records/${recordId}`)
  },

  // 获取学习进度统计
  getLearningProgressStats(params?: { class_id?: number; group_id?: number }) {
    return request.get('/api/admin/statistics/progress', { params })
  },

  // 获取提交趋势统计
  getSubmissionTrends(params?: { days?: number; class_id?: number }) {
    return request.get('/api/admin/statistics/trends', { params })
  }
}

