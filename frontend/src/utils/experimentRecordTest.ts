/**
 * 实验记录表系统测试工具
 * 用于验证不同实验类型的数据解析和JSON生成功能
 */

import { 
  parseExperimentData, 
  createExperimentRecordJson,
  type ExperimentRecord,
  type CurrentControlCircuitData,
  type OscilloscopeData
} from '@/services/experimentDataParser'

// 测试数据
export const testData = {
  // 制流电路实验测试数据
  currentControlCircuit: {
    student_id: "2021001",
    student_name: "张三",
    k1Data: [
      { ratio: 0.1, current: 0.0012 },
      { ratio: 0.2, current: 0.0024 },
      { ratio: 0.3, current: 0.0036 },
      { ratio: 0.4, current: 0.0048 },
      { ratio: 0.5, current: 0.0060 }
    ],
    k01Data: [
      { ratio: 0.1, current: 0.00012 },
      { ratio: 0.2, current: 0.00024 },
      { ratio: 0.3, current: 0.00036 },
      { ratio: 0.4, current: 0.00048 },
      { ratio: 0.5, current: 0.00060 }
    ],
    temperature: 25.5,
    humidity: 60,
    notes: "实验过程正常，数据稳定"
  },

  // 示波器实验测试数据
  oscilloscope: {
    student_id: "2021002",
    student_name: "李四",
    frequency: 1000,
    amplitude: 5.0,
    waveform_type: "正弦波",
    measurements: [
      { time: 0.000000, voltage: 0.0000 },
      { time: 0.000100, voltage: 2.9389 },
      { time: 0.000200, voltage: 4.7553 },
      { time: 0.000300, voltage: 4.7553 },
      { time: 0.000400, voltage: 2.9389 },
      { time: 0.000500, voltage: 0.0000 },
      { time: 0.000600, voltage: -2.9389 },
      { time: 0.000700, voltage: -4.7553 },
      { time: 0.000800, voltage: -4.7553 },
      { time: 0.000900, voltage: -2.9389 },
      { time: 0.001000, voltage: 0.0000 }
    ],
    notes: "波形稳定，无明显失真"
  },

  // 通用实验测试数据
  generic: {
    student_id: "2021003",
    student_name: "王五",
    experiment_parameter1: 123.45,
    experiment_parameter2: "测试值",
    measurements: [
      { item: "测量1", value: 67.89, unit: "V" },
      { item: "测量2", value: 98.76, unit: "A" }
    ],
    notes: "通用格式实验数据"
  }
}

// 模拟实验记录
export const mockExperimentRecord: ExperimentRecord = {
  id: 1,
  student_id: 1,
  experiment_type_id: 1,
  experiment_code: "current_control_circuit",
  experiment_name: "制流电路实验",
  submission_data: "",
  plot_data: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",
  analysis_result: "这是AI分析结果的示例文本。实验数据符合预期，测量结果准确。",
  is_passed: true,
  score: 95,
  submitted_at: new Date().toISOString(),
  status: "approved"
}

/**
 * 测试制流电路实验数据解析
 */
export function testCurrentControlCircuitParsing() {
  console.log("=== 测试制流电路实验数据解析 ===")
  
  const result = parseExperimentData("current_control_circuit", testData.currentControlCircuit)
  
  console.log("解析结果:", result)
  console.log("是否有效:", result.isValid)
  
  if (result.errors) {
    console.log("错误信息:", result.errors)
  }

  if (result.isValid && result.data) {
    const record = { ...mockExperimentRecord, submission_data: JSON.stringify(testData.currentControlCircuit) }
    const jsonRecord = createExperimentRecordJson("current_control_circuit", result.data, record)
    console.log("生成的JSON记录:", JSON.stringify(jsonRecord, null, 2))
  }
  
  return result
}

/**
 * 测试示波器实验数据解析
 */
export function testOscilloscopeParsing() {
  console.log("=== 测试示波器实验数据解析 ===")
  
  const result = parseExperimentData("oscilloscope", testData.oscilloscope)
  
  console.log("解析结果:", result)
  console.log("是否有效:", result.isValid)
  
  if (result.errors) {
    console.log("错误信息:", result.errors)
  }

  if (result.isValid && result.data) {
    const record = { 
      ...mockExperimentRecord, 
      experiment_code: "oscilloscope",
      experiment_name: "示波器实验",
      submission_data: JSON.stringify(testData.oscilloscope) 
    }
    const jsonRecord = createExperimentRecordJson("oscilloscope", result.data, record)
    console.log("生成的JSON记录:", JSON.stringify(jsonRecord, null, 2))
  }
  
  return result
}

/**
 * 测试通用实验数据解析
 */
export function testGenericParsing() {
  console.log("=== 测试通用实验数据解析 ===")
  
  const result = parseExperimentData("unknown_experiment", testData.generic)
  
  console.log("解析结果:", result)
  console.log("是否有效:", result.isValid)
  
  if (result.errors) {
    console.log("错误信息:", result.errors)
  }

  const record = { 
    ...mockExperimentRecord, 
    experiment_code: "unknown_experiment",
    experiment_name: "未知实验",
    submission_data: JSON.stringify(testData.generic) 
  }
  const jsonRecord = createExperimentRecordJson("unknown_experiment", result.data, record)
  console.log("生成的JSON记录:", JSON.stringify(jsonRecord, null, 2))
  
  return result
}

/**
 * 测试错误数据处理
 */
export function testErrorHandling() {
  console.log("=== 测试错误数据处理 ===")
  
  // 测试无效JSON
  const invalidJson = "{ invalid json"
  const result1 = parseExperimentData("current_control_circuit", invalidJson)
  console.log("无效JSON解析结果:", result1)
  
  // 测试缺失字段
  const incompleteData = {
    student_id: "2021001"
    // 缺少其他必需字段
  }
  const result2 = parseExperimentData("current_control_circuit", incompleteData)
  console.log("不完整数据解析结果:", result2)
  
  return { result1, result2 }
}

/**
 * 运行所有测试
 */
export function runAllTests() {
  console.log("开始运行实验记录表系统测试...")
  
  try {
    testCurrentControlCircuitParsing()
    testOscilloscopeParsing()
    testGenericParsing()
    testErrorHandling()
    
    console.log("所有测试完成！")
  } catch (error) {
    console.error("测试过程中出现错误:", error)
  }
}

// 在开发环境中可以直接运行测试
if (import.meta.env.DEV) {
  // runAllTests()
}
