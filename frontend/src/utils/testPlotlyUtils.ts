/**
 * 测试前端绘图功能
 */

import { generateCurrentControlCircuitPlot } from './plotlyUtils'

// 测试数据
const testData = {
  k1Data: [
    { ratio: 0.1, current: 0.0012 },
    { ratio: 0.2, current: 0.0024 },
    { ratio: 0.3, current: 0.0036 },
    { ratio: 0.4, current: 0.0048 },
    { ratio: 0.5, current: 0.0060 }
  ],
  k01Data: [
    { ratio: 0.1, current: 0.00012 },
    { ratio: 0.2, current: 0.00024 },
    { ratio: 0.3, current: 0.00036 },
    { ratio: 0.4, current: 0.00048 },
    { ratio: 0.5, current: 0.00060 }
  ]
}

// 测试函数
export function testPlotlyUtils() {
  console.log('开始测试前端绘图功能...')
  
  try {
    const plotConfig = generateCurrentControlCircuitPlot(testData, {
      width: 800,
      height: 600,
      smooth: true,
      lang: 'zh'
    })
    
    console.log('✅ 图表配置生成成功:', plotConfig)
    
    // 验证返回的数据结构
    if (plotConfig.data && plotConfig.layout && plotConfig.config) {
      console.log('✅ 数据结构验证通过')
      console.log('- 数据轨迹数量:', plotConfig.data.length)
      console.log('- 图表标题:', plotConfig.layout.title?.text)
      console.log('- X轴标题:', plotConfig.layout.xaxis?.title)
      console.log('- Y轴标题:', plotConfig.layout.yaxis?.title)
      return true
    } else {
      console.error('❌ 数据结构验证失败')
      return false
    }
  } catch (error) {
    console.error('❌ 测试失败:', error)
    return false
  }
}

// 如果直接运行此文件，执行测试
if (typeof window !== 'undefined') {
  // 在浏览器环境中可以直接调用
  console.log('前端绘图工具测试可用，请在控制台调用 testPlotlyUtils() 进行测试')
}
