/**
 * Plotly绘图工具函数
 * 用于在前端生成各种实验图表
 */

// 制流电路实验数据接口
export interface CurrentControlCircuitData {
  k1Data: Array<{
    ratio: number
    current: number | null
  }>
  k01Data: Array<{
    ratio: number
    current: number | null
  }>
}

// 图表配置接口
export interface PlotConfig {
  width?: number
  height?: number
  title?: string
  xAxisTitle?: string
  yAxisTitle?: string
  smooth?: boolean
  lang?: 'zh' | 'en'
}

// 图表数据接口
export interface PlotData {
  x: number[]
  y: number[]
  mode: string
  name: string
  line?: any
  marker?: any
}

/**
 * 生成制流电路实验图表配置
 */
export function generateCurrentControlCircuitPlot(
  data: CurrentControlCircuitData,
  config: PlotConfig = {}
): any {
  const {
    width = 800,
    height = 600,
    title = '制流电路实验 - 电流与接入比例关系',
    xAxisTitle = '接入比例',
    yAxisTitle = '电流 (A)',
    smooth = true,
    lang = 'zh'
  } = config

  // 提取数据
  const k1_ratios = data.k1Data.map(item => item.ratio).filter(r => r !== null)
  const k1_currents = data.k1Data.map(item => item.current).filter(c => c !== null)
  const k01_ratios = data.k01Data.map(item => item.ratio).filter(r => r !== null)
  const k01_currents = data.k01Data.map(item => item.current).filter(c => c !== null)

  if (k1_ratios.length === 0 && k01_ratios.length === 0) {
    throw new Error('没有有效的数据点')
  }

  // 数据插值函数（简化版）
  const densify = (x: number[], y: number[]) => {
    if (x.length < 2) return { x, y }
    
    const newX: number[] = []
    const newY: number[] = []
    
    for (let i = 0; i < x.length - 1; i++) {
      newX.push(x[i])
      newY.push(y[i])
      
      // 在两点之间插入中间点
      const steps = 10
      for (let j = 1; j < steps; j++) {
        const t = j / steps
        const interpX = x[i] + t * (x[i + 1] - x[i])
        const interpY = y[i] + t * (y[i + 1] - y[i])
        newX.push(interpX)
        newY.push(interpY)
      }
    }
    
    // 添加最后一个点
    newX.push(x[x.length - 1])
    newY.push(y[y.length - 1])
    
    return { x: newX, y: newY }
  }

  // 准备绘图数据
  const traces: any[] = []

  // k=1数据
  if (k1_ratios.length > 0) {
    if (smooth && k1_ratios.length >= 2) {
      const { x: smoothX, y: smoothY } = densify(k1_ratios, k1_currents)
      traces.push({
        x: smoothX,
        y: smoothY,
        mode: 'lines',
        name: 'k=1',
        line: { color: '#1f77b4', width: 3 },
        type: 'scatter'
      })
    }
    
    // 原始数据点
    traces.push({
      x: k1_ratios,
      y: k1_currents,
      mode: 'markers',
      name: 'k=1 原始点',
      marker: { size: 7, symbol: 'circle', color: '#1f77b4' },
      type: 'scatter'
    })
  }

  // k=0.1数据
  if (k01_ratios.length > 0) {
    if (smooth && k01_ratios.length >= 2) {
      const { x: smoothX, y: smoothY } = densify(k01_ratios, k01_currents)
      traces.push({
        x: smoothX,
        y: smoothY,
        mode: 'lines',
        name: 'k=0.1',
        line: { color: '#ff7f0e', width: 3 },
        type: 'scatter'
      })
    }
    
    // 原始数据点
    traces.push({
      x: k01_ratios,
      y: k01_currents,
      mode: 'markers',
      name: 'k=0.1 原始点',
      marker: { size: 7, symbol: 'circle', color: '#ff7f0e' },
      type: 'scatter'
    })
  }

  // 图表布局
  const layout = {
    title: {
      text: title,
      x: 0.5,
      xanchor: 'center',
      font: { size: 18 }
    },
    xaxis: {
      title: xAxisTitle,
      showgrid: true,
      gridwidth: 1,
      gridcolor: 'rgba(128,128,128,0.3)',
      title_font: { size: 14 }
    },
    yaxis: {
      title: yAxisTitle,
      showgrid: true,
      gridwidth: 1,
      gridcolor: 'rgba(128,128,128,0.3)',
      title_font: { size: 14 }
    },
    legend: {
      x: 0.02,
      y: 0.98,
      bgcolor: 'rgba(255,255,255,0.8)',
      bordercolor: 'rgba(0,0,0,0.2)',
      borderwidth: 1
    },
    width,
    height,
    template: 'plotly_white',
    font: { size: 12 },
    plot_bgcolor: 'rgba(0,0,0,0)',
    paper_bgcolor: 'rgba(0,0,0,0)'
  }

  return {
    data: traces,
    layout,
    config: {
      responsive: true,
      displayModeBar: true,
      modeBarButtonsToRemove: ['pan2d', 'lasso2d', 'select2d'],
      displaylogo: false
    }
  }
}

/**
 * 生成示波器实验图表配置（预留）
 */
export function generateOscilloscopePlot(
  data: any,
  config: PlotConfig = {}
): any {
  // 示波器实验的绘图逻辑
  // 这里先返回一个简单的示例
  return {
    data: [{
      x: [1, 2, 3, 4],
      y: [10, 11, 12, 13],
      type: 'scatter'
    }],
    layout: {
      title: '示波器实验',
      width: config.width || 800,
      height: config.height || 600
    }
  }
}

/**
 * 将plotly图表导出为PNG base64
 */
export async function exportPlotToPNG(plotElement: any): Promise<string> {
  if (!plotElement || !plotElement.toImage) {
    throw new Error('无效的plotly图表元素')
  }
  
  try {
    const imageData = await plotElement.toImage({
      format: 'png',
      width: 800,
      height: 600
    })
    
    // 移除data:image/png;base64,前缀
    return imageData.replace(/^data:image\/png;base64,/, '')
  } catch (error) {
    console.error('导出图片失败:', error)
    throw new Error('导出图片失败')
  }
}
